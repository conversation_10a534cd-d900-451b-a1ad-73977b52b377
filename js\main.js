// 浏览器环境飞行棋游戏主文件
import './render.js'; // 初始化Canvas
import { getCanvas } from './utils/environment.js'; // 导入环境适配器
import { handleError, safeExecute, ERROR_TYPES, ERROR_LEVELS } from './utils/errorHandler.js'; // 导入错误处理器
import DataBus from './databus.js'; // 导入数据管理类
import HallScene from './scenes/hall.js'; // 导入大厅场景
import GameScene from './scenes/game.js'; // 导入游戏场景
import ResultScene from './scenes/result.js'; // 导入结果场景

// 确保Canvas和全局对象可用
const canvas = safeExecute(
  () => getCanvas(),
  null,
  ERROR_TYPES.CANVAS
);

if (!canvas) {
  handleError({
    type: ERROR_TYPES.CANVAS,
    level: ERROR_LEVELS.FATAL,
    message: '无法获取Canvas元素，游戏无法启动'
  });
  throw new Error('Canvas初始化失败');
}

const ctx = safeExecute(
  () => canvas.getContext('2d'),
  null,
  ERROR_TYPES.CANVAS
);

if (!ctx) {
  handleError({
    type: ERROR_TYPES.CANVAS,
    level: ERROR_LEVELS.FATAL,
    message: '无法获取Canvas 2D上下文，游戏无法启动'
  });
  throw new Error('Canvas上下文初始化失败');
}

// 确保全局对象存在
if (typeof window.GameGlobal === 'undefined') {
  window.GameGlobal = {};
}

// 全局数据管理实例
window.GameGlobal.databus = new DataBus();

// 添加全局错误处理（浏览器环境）
if (typeof wx !== 'undefined' && wx.onError) {
  wx.onError(function(res) {
    console.log('捕获到全局错误:', res.message);
  });
} else {
  window.addEventListener('error', function(e) {
    console.log('捕获到全局错误:', e.message);
  });

  window.addEventListener('unhandledrejection', function(e) {
    console.log('捕获到未处理的Promise错误:', e.reason);
  });
}

/**
 * 游戏主函数
 */
export default class Main {
  aniId = 0; // 用于存储动画帧的ID
  currentScene = null; // 当前场景
  scenes = {}; // 场景集合

  constructor() {
    // 初始化游戏
    this.init();
    // 开始游戏主循环
    this.loop();
  }

  /**
   * 初始化游戏
   */
  init() {
    // 重置游戏数据
    window.GameGlobal.databus.reset();

    // 创建场景
    this.createScenes();

    // 默认显示大厅场景
    this.switchScene('hall');

    console.log('游戏初始化完成');
  }

  /**
   * 创建场景
   */
  createScenes() {
    safeExecute(() => {
      // 创建大厅场景
      this.scenes.hall = new HallScene({
        onStartGame: (gameMode, playerCount) => {
          // 设置游戏模式和玩家数量
          window.GameGlobal.databus.gameMode = gameMode;

          // 初始化游戏
          window.GameGlobal.databus.initGame(playerCount, gameMode);

          // 切换到游戏场景
          this.switchScene('game');
        }
      });

      // 创建游戏场景
      this.scenes.game = new GameScene({
        onGameOver: (winner) => {
          // 游戏结束，切换到结果场景
          this.switchScene('result', { winner });
        },
        onBack: () => {
          // 返回大厅
          this.switchScene('hall');
        }
      });

      // 创建结果场景
      this.scenes.result = new ResultScene({
        onRestart: () => {
          // 重新开始游戏
          this.switchScene('game');
        },
        onBack: () => {
          // 返回大厅
          this.switchScene('hall');
        }
      });

      // 初始化所有场景
      Object.values(this.scenes).forEach(scene => {
        safeExecute(
          () => scene.init(),
          null,
          ERROR_TYPES.GAME_LOGIC
        );
      });
    }, null, ERROR_TYPES.GAME_LOGIC);
  }
  
  /**
   * 切换场景
   * @param {string} sceneName - 场景名称
   * @param {Object} params - 传递给场景的参数
   */
  switchScene(sceneName, params = {}) {
    // 如果有当前场景，先退出
    if (this.currentScene) {
      this.currentScene.exit();
    }
    
    // 切换到新场景
    this.currentScene = this.scenes[sceneName];
    
    // 如果有参数，设置参数
    if (params) {
      this.currentScene.setParams && this.currentScene.setParams(params);
    }
    
    // 进入新场景
    this.currentScene.enter();
  }

  /**
   * 渲染游戏画面
   */
  render() {
    safeExecute(() => {
      // 清空画布
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // 渲染当前场景
      if (this.currentScene) {
        this.currentScene.render(ctx);
      }
    }, null, ERROR_TYPES.CANVAS);
  }

  /**
   * 更新游戏逻辑
   */
  update() {
    safeExecute(() => {
      // 增加帧数
      window.GameGlobal.databus.frame++;

      // 更新当前场景
      if (this.currentScene) {
        this.currentScene.update();
      }
    }, null, ERROR_TYPES.GAME_LOGIC);
  }

  /**
   * 游戏循环
   */
  loop() {
    safeExecute(() => {
      // 更新游戏逻辑
      this.update();

      // 渲染游戏画面
      this.render();

      // 循环调用，形成帧动画
      this.aniId = requestAnimationFrame(this.loop.bind(this));
    }, null, ERROR_TYPES.GAME_LOGIC);
  }
}
