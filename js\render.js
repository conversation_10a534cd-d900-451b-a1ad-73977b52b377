// 环境适配
import { getCanvas, getWindowInfo, isWeChatGame } from './utils/environment.js';

// 获取Canvas元素
const canvas = getCanvas();

// 设置全局Canvas引用
if (typeof window !== 'undefined') {
  window.canvas = canvas;
  window.GameGlobal = window.GameGlobal || {};
  window.GameGlobal.canvas = canvas;
}

// 获取窗口信息已通过环境适配器提供

// 设置画布尺寸
const windowInfo = getWindowInfo();
canvas.width = Math.min(800, windowInfo.screenWidth);
canvas.height = Math.min(600, windowInfo.screenHeight);

// 导出屏幕尺寸常量
export const SCREEN_WIDTH = canvas.width;
export const SCREEN_HEIGHT = canvas.height;

// 响应式处理
window.addEventListener('resize', () => {
  const newWindowInfo = getWindowInfo();
  canvas.width = Math.min(800, newWindowInfo.screenWidth);
  canvas.height = Math.min(600, newWindowInfo.screenHeight);

  // 更新全局尺寸变量
  if (window.SCREEN_WIDTH !== undefined) {
    window.SCREEN_WIDTH = canvas.width;
    window.SCREEN_HEIGHT = canvas.height;
  }
});

// 创建微信小游戏API的浏览器兼容层
if (!isWeChatGame()) {
  window.wx = {
    createCanvas: () => canvas,
    getWindowInfo: getWindowInfo,
    getSystemInfoSync: getWindowInfo,
    onError: (callback) => {
      window.addEventListener('error', (e) => {
        callback({ message: e.message, stack: e.error?.stack });
      });
    }
  };
}