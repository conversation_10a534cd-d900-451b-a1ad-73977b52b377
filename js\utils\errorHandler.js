/**
 * 错误处理工具
 * 提供统一的错误处理和降级方案
 */

import { environmentDetector, showError } from './environment.js';

/**
 * 错误类型枚举
 */
export const ERROR_TYPES = {
  ENVIRONMENT: 'environment',
  CANVAS: 'canvas',
  AUDIO: 'audio',
  NETWORK: 'network',
  GAME_LOGIC: 'game_logic',
  UNKNOWN: 'unknown'
};

/**
 * 错误级别枚举
 */
export const ERROR_LEVELS = {
  FATAL: 'fatal',      // 致命错误，游戏无法继续
  ERROR: 'error',      // 严重错误，影响功能
  WARNING: 'warning',  // 警告，不影响核心功能
  INFO: 'info'         // 信息，仅记录
};

/**
 * 错误处理器
 */
class ErrorHandler {
  constructor() {
    this.errorLog = [];
    this.maxLogSize = 100;
    this.errorCallbacks = new Map();
    this.fallbackStrategies = new Map();
    
    // 初始化默认降级策略
    this.initDefaultFallbacks();
    
    // 注册全局错误处理
    this.registerGlobalHandlers();
  }

  /**
   * 初始化默认降级策略
   */
  initDefaultFallbacks() {
    // Canvas相关错误的降级策略
    this.fallbackStrategies.set(ERROR_TYPES.CANVAS, {
      strategy: 'mock',
      handler: () => {
        console.warn('Canvas不可用，使用模拟Canvas');
        return this.createMockCanvas();
      }
    });

    // 音频相关错误的降级策略
    this.fallbackStrategies.set(ERROR_TYPES.AUDIO, {
      strategy: 'silent',
      handler: () => {
        console.warn('音频不可用，使用静默模式');
        return this.createMockAudio();
      }
    });

    // 网络相关错误的降级策略
    this.fallbackStrategies.set(ERROR_TYPES.NETWORK, {
      strategy: 'offline',
      handler: () => {
        console.warn('网络不可用，使用离线模式');
        return { offline: true };
      }
    });
  }

  /**
   * 注册全局错误处理
   */
  registerGlobalHandlers() {
    // 处理未捕获的JavaScript错误
    if (typeof window !== 'undefined') {
      window.addEventListener('error', (event) => {
        this.handleError({
          type: ERROR_TYPES.UNKNOWN,
          level: ERROR_LEVELS.ERROR,
          message: event.message,
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          error: event.error
        });
      });

      // 处理未捕获的Promise错误
      window.addEventListener('unhandledrejection', (event) => {
        this.handleError({
          type: ERROR_TYPES.UNKNOWN,
          level: ERROR_LEVELS.ERROR,
          message: 'Unhandled Promise Rejection',
          reason: event.reason
        });
      });
    }

    // 微信小游戏环境的错误处理
    if (environmentDetector.isWeChatGame() && typeof wx !== 'undefined') {
      wx.onError && wx.onError((error) => {
        this.handleError({
          type: ERROR_TYPES.UNKNOWN,
          level: ERROR_LEVELS.ERROR,
          message: error.message,
          stack: error.stack
        });
      });
    }
  }

  /**
   * 处理错误
   * @param {Object} errorInfo - 错误信息
   */
  handleError(errorInfo) {
    const error = {
      id: this.generateErrorId(),
      timestamp: new Date().toISOString(),
      environment: environmentDetector.getEnvironmentType(),
      ...errorInfo
    };

    // 记录错误
    this.logError(error);

    // 执行错误回调
    this.executeErrorCallbacks(error);

    // 尝试降级处理
    const fallbackResult = this.tryFallback(error);

    // 根据错误级别决定是否显示给用户
    if (error.level === ERROR_LEVELS.FATAL || error.level === ERROR_LEVELS.ERROR) {
      this.showUserError(error);
    }

    return fallbackResult;
  }

  /**
   * 生成错误ID
   */
  generateErrorId() {
    return 'err_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * 记录错误
   */
  logError(error) {
    this.errorLog.push(error);
    
    // 限制日志大小
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog.shift();
    }

    // 控制台输出
    const logMethod = this.getLogMethod(error.level);
    logMethod(`[${error.level.toUpperCase()}] ${error.type}: ${error.message}`, error);
  }

  /**
   * 获取日志方法
   */
  getLogMethod(level) {
    switch (level) {
      case ERROR_LEVELS.FATAL:
      case ERROR_LEVELS.ERROR:
        return console.error;
      case ERROR_LEVELS.WARNING:
        return console.warn;
      case ERROR_LEVELS.INFO:
      default:
        return console.log;
    }
  }

  /**
   * 执行错误回调
   */
  executeErrorCallbacks(error) {
    const callbacks = this.errorCallbacks.get(error.type) || [];
    callbacks.forEach(callback => {
      try {
        callback(error);
      } catch (e) {
        console.error('错误回调执行失败:', e);
      }
    });
  }

  /**
   * 尝试降级处理
   */
  tryFallback(error) {
    const fallback = this.fallbackStrategies.get(error.type);
    if (fallback) {
      try {
        return fallback.handler(error);
      } catch (e) {
        console.error('降级策略执行失败:', e);
      }
    }
    return null;
  }

  /**
   * 显示用户错误
   */
  showUserError(error) {
    let message = this.getUserFriendlyMessage(error);
    showError(message);
  }

  /**
   * 获取用户友好的错误信息
   */
  getUserFriendlyMessage(error) {
    const messageMap = {
      [ERROR_TYPES.ENVIRONMENT]: '运行环境不支持，请使用支持的浏览器或微信小游戏',
      [ERROR_TYPES.CANVAS]: '图形渲染出现问题，请刷新页面重试',
      [ERROR_TYPES.AUDIO]: '音频播放出现问题，游戏将以静音模式运行',
      [ERROR_TYPES.NETWORK]: '网络连接出现问题，请检查网络设置',
      [ERROR_TYPES.GAME_LOGIC]: '游戏逻辑出现错误，请重新开始游戏'
    };

    return messageMap[error.type] || '游戏出现未知错误，请刷新页面重试';
  }

  /**
   * 创建模拟Canvas
   */
  createMockCanvas() {
    const mockMethods = [
      'clearRect', 'fillRect', 'strokeRect', 'fillText', 'strokeText',
      'drawImage', 'save', 'restore', 'translate', 'rotate', 'scale',
      'beginPath', 'closePath', 'moveTo', 'lineTo', 'arc', 'fill', 'stroke'
    ];

    const canvas = {
      width: 800,
      height: 600,
      getBoundingClientRect: () => ({ left: 0, top: 0, width: 800, height: 600 }),
      addEventListener: () => {},
      removeEventListener: () => {},
      getContext: () => {
        const context = {};
        mockMethods.forEach(method => {
          context[method] = () => {};
        });
        
        // 添加属性
        context.fillStyle = '#000000';
        context.strokeStyle = '#000000';
        context.lineWidth = 1;
        context.font = '12px Arial';
        context.textAlign = 'start';
        context.textBaseline = 'alphabetic';
        
        return context;
      }
    };

    return canvas;
  }

  /**
   * 创建模拟音频
   */
  createMockAudio() {
    return {
      play: () => Promise.resolve(),
      pause: () => {},
      stop: () => {},
      volume: 1,
      loop: false,
      currentTime: 0,
      duration: 0,
      onEnded: () => {},
      onError: () => {}
    };
  }

  /**
   * 注册错误回调
   */
  onError(type, callback) {
    if (!this.errorCallbacks.has(type)) {
      this.errorCallbacks.set(type, []);
    }
    this.errorCallbacks.get(type).push(callback);
  }

  /**
   * 注册降级策略
   */
  setFallbackStrategy(type, strategy, handler) {
    this.fallbackStrategies.set(type, { strategy, handler });
  }

  /**
   * 获取错误日志
   */
  getErrorLog() {
    return [...this.errorLog];
  }

  /**
   * 清空错误日志
   */
  clearErrorLog() {
    this.errorLog = [];
  }

  /**
   * 安全执行函数
   */
  safeExecute(fn, fallback = null, errorType = ERROR_TYPES.UNKNOWN) {
    try {
      return fn();
    } catch (error) {
      this.handleError({
        type: errorType,
        level: ERROR_LEVELS.ERROR,
        message: error.message,
        stack: error.stack
      });
      
      return fallback;
    }
  }

  /**
   * 安全执行异步函数
   */
  async safeExecuteAsync(fn, fallback = null, errorType = ERROR_TYPES.UNKNOWN) {
    try {
      return await fn();
    } catch (error) {
      this.handleError({
        type: errorType,
        level: ERROR_LEVELS.ERROR,
        message: error.message,
        stack: error.stack
      });
      
      return fallback;
    }
  }
}

// 创建全局实例
export const errorHandler = new ErrorHandler();

// 导出便捷方法
export const handleError = (errorInfo) => errorHandler.handleError(errorInfo);
export const safeExecute = (fn, fallback, errorType) => errorHandler.safeExecute(fn, fallback, errorType);
export const safeExecuteAsync = (fn, fallback, errorType) => errorHandler.safeExecuteAsync(fn, fallback, errorType);
export const onError = (type, callback) => errorHandler.onError(type, callback);
