/**
 * 微信小游戏兼容性工具
 * 处理微信小游戏环境下的特殊问题和兼容性
 */

import { environmentDetector } from './environment.js';
import { handleError, ERROR_TYPES, ERROR_LEVELS } from './errorHandler.js';

/**
 * 微信小游戏兼容性管理器
 */
class WeChatCompatibilityManager {
  constructor() {
    this.isWeChatGame = environmentDetector.isWeChatGame();
    this.initialized = false;
    this.polyfills = new Map();
    
    if (this.isWeChatGame) {
      this.initWeChatPolyfills();
    }
  }

  /**
   * 初始化微信小游戏兼容性补丁
   */
  initWeChatPolyfills() {
    try {
      // 修复SharedArrayBuffer警告（这个警告来自微信开发者工具，无法完全消除）
      this.suppressSharedArrayBufferWarning();
      
      // 修复触摸事件兼容性
      this.fixTouchEventCompatibility();
      
      // 修复Canvas相关问题
      this.fixCanvasCompatibility();
      
      // 修复图片加载问题
      this.fixImageLoadingCompatibility();
      
      // 修复音频播放问题
      this.fixAudioCompatibility();
      
      this.initialized = true;
      console.log('微信小游戏兼容性初始化完成');
    } catch (error) {
      handleError({
        type: ERROR_TYPES.ENVIRONMENT,
        level: ERROR_LEVELS.WARNING,
        message: '微信小游戏兼容性初始化失败: ' + error.message
      });
    }
  }

  /**
   * 抑制SharedArrayBuffer警告（仅记录，无法完全消除）
   */
  suppressSharedArrayBufferWarning() {
    // 这个警告来自微信开发者工具的内置库，我们只能记录并提供说明
    console.info('注意: SharedArrayBuffer警告来自微信开发者工具内置库，不影响游戏功能');
    
    // 如果有全局的SharedArrayBuffer，可以尝试一些兼容性处理
    if (typeof SharedArrayBuffer !== 'undefined') {
      // 记录SharedArrayBuffer的存在
      this.polyfills.set('SharedArrayBuffer', true);
    }
  }

  /**
   * 修复触摸事件兼容性
   */
  fixTouchEventCompatibility() {
    // 确保触摸事件对象有正确的结构
    const originalOnTouchStart = wx.onTouchStart;
    const originalOnTouchMove = wx.onTouchMove;
    const originalOnTouchEnd = wx.onTouchEnd;

    // 包装触摸开始事件
    wx.onTouchStart = (callback) => {
      const wrappedCallback = (event) => {
        try {
          // 确保事件对象有正确的结构
          const normalizedEvent = this.normalizeTouchEvent(event);
          callback(normalizedEvent);
        } catch (error) {
          handleError({
            type: ERROR_TYPES.UNKNOWN,
            level: ERROR_LEVELS.WARNING,
            message: '触摸开始事件处理失败: ' + error.message
          });
        }
      };
      
      if (originalOnTouchStart) {
        originalOnTouchStart.call(wx, wrappedCallback);
      }
    };

    // 包装触摸移动事件
    wx.onTouchMove = (callback) => {
      const wrappedCallback = (event) => {
        try {
          const normalizedEvent = this.normalizeTouchEvent(event);
          callback(normalizedEvent);
        } catch (error) {
          handleError({
            type: ERROR_TYPES.UNKNOWN,
            level: ERROR_LEVELS.WARNING,
            message: '触摸移动事件处理失败: ' + error.message
          });
        }
      };
      
      if (originalOnTouchMove) {
        originalOnTouchMove.call(wx, wrappedCallback);
      }
    };

    // 包装触摸结束事件
    wx.onTouchEnd = (callback) => {
      const wrappedCallback = (event) => {
        try {
          const normalizedEvent = this.normalizeTouchEvent(event);
          callback(normalizedEvent);
        } catch (error) {
          handleError({
            type: ERROR_TYPES.UNKNOWN,
            level: ERROR_LEVELS.WARNING,
            message: '触摸结束事件处理失败: ' + error.message
          });
        }
      };
      
      if (originalOnTouchEnd) {
        originalOnTouchEnd.call(wx, wrappedCallback);
      }
    };

    this.polyfills.set('TouchEvents', true);
  }

  /**
   * 标准化触摸事件对象
   */
  normalizeTouchEvent(event) {
    if (!event) {
      return { touches: [], changedTouches: [] };
    }

    // 确保touches数组存在
    if (!event.touches) {
      event.touches = [];
    }

    // 确保changedTouches数组存在
    if (!event.changedTouches) {
      event.changedTouches = event.touches;
    }

    // 确保每个触摸点有正确的属性
    event.touches = event.touches.map(touch => ({
      clientX: touch.clientX || touch.x || 0,
      clientY: touch.clientY || touch.y || 0,
      pageX: touch.pageX || touch.clientX || touch.x || 0,
      pageY: touch.pageY || touch.clientY || touch.y || 0,
      identifier: touch.identifier || 0,
      ...touch
    }));

    return event;
  }

  /**
   * 修复Canvas兼容性
   */
  fixCanvasCompatibility() {
    // 确保wx.createCanvas存在
    if (!wx.createCanvas) {
      wx.createCanvas = () => {
        console.warn('wx.createCanvas不存在，返回模拟Canvas');
        return this.createMockCanvas();
      };
    }

    this.polyfills.set('Canvas', true);
  }

  /**
   * 创建模拟Canvas
   */
  createMockCanvas() {
    return {
      width: 800,
      height: 600,
      getContext: () => ({
        clearRect: () => {},
        fillRect: () => {},
        strokeRect: () => {},
        fillText: () => {},
        strokeText: () => {},
        drawImage: () => {},
        save: () => {},
        restore: () => {},
        translate: () => {},
        rotate: () => {},
        scale: () => {},
        beginPath: () => {},
        closePath: () => {},
        moveTo: () => {},
        lineTo: () => {},
        arc: () => {},
        fill: () => {},
        stroke: () => {},
        fillStyle: '#000000',
        strokeStyle: '#000000',
        lineWidth: 1,
        font: '12px Arial'
      }),
      getBoundingClientRect: () => ({ left: 0, top: 0, width: 800, height: 600 })
    };
  }

  /**
   * 修复图片加载兼容性
   */
  fixImageLoadingCompatibility() {
    // 确保wx.createImage存在
    if (!wx.createImage) {
      wx.createImage = () => {
        console.warn('wx.createImage不存在，返回模拟Image');
        return new Image();
      };
    }

    this.polyfills.set('Image', true);
  }

  /**
   * 修复音频兼容性
   */
  fixAudioCompatibility() {
    // 确保wx.createInnerAudioContext存在
    if (!wx.createInnerAudioContext) {
      wx.createInnerAudioContext = () => {
        console.warn('wx.createInnerAudioContext不存在，返回模拟Audio');
        return {
          play: () => Promise.resolve(),
          pause: () => {},
          stop: () => {},
          destroy: () => {},
          volume: 1,
          loop: false,
          src: '',
          onPlay: () => {},
          onPause: () => {},
          onStop: () => {},
          onEnded: () => {},
          onError: () => {}
        };
      };
    }

    this.polyfills.set('Audio', true);
  }

  /**
   * 获取兼容性状态
   */
  getCompatibilityStatus() {
    return {
      isWeChatGame: this.isWeChatGame,
      initialized: this.initialized,
      polyfills: Object.fromEntries(this.polyfills),
      hasSharedArrayBufferWarning: typeof SharedArrayBuffer !== 'undefined'
    };
  }

  /**
   * 检查特定功能的兼容性
   */
  checkFeatureCompatibility(feature) {
    const checks = {
      canvas: () => typeof wx.createCanvas === 'function',
      image: () => typeof wx.createImage === 'function',
      audio: () => typeof wx.createInnerAudioContext === 'function',
      touch: () => typeof wx.onTouchStart === 'function',
      storage: () => typeof wx.setStorageSync === 'function'
    };

    return checks[feature] ? checks[feature]() : false;
  }

  /**
   * 安全执行微信API
   */
  safeWxCall(apiName, ...args) {
    try {
      if (wx && typeof wx[apiName] === 'function') {
        return wx[apiName](...args);
      } else {
        console.warn(`微信API ${apiName} 不存在`);
        return null;
      }
    } catch (error) {
      handleError({
        type: ERROR_TYPES.UNKNOWN,
        level: ERROR_LEVELS.WARNING,
        message: `微信API ${apiName} 调用失败: ${error.message}`
      });
      return null;
    }
  }
}

// 创建全局实例
export const wechatCompat = new WeChatCompatibilityManager();

// 导出便捷方法
export const getCompatibilityStatus = () => wechatCompat.getCompatibilityStatus();
export const checkFeatureCompatibility = (feature) => wechatCompat.checkFeatureCompatibility(feature);
export const safeWxCall = (apiName, ...args) => wechatCompat.safeWxCall(apiName, ...args);
