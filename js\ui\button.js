import Sprite from '../base/sprite.js';
import { ASSETS } from '../config/gameConfig.js';
import { COLORS, SHADOWS, BORDER_RADIUS, SPACING, TYPOGRAPHY, COMPONENT_STYLES, DESIGN_UTILS } from '../config/designSystem.js';

/**
 * 按钮组件 - 扁平化设计
 * 实现现代化的交互按钮功能
 */
export default class Button {
  // 按钮位置和大小
  x = 0;
  y = 0;
  width = 100;
  height = 40;
  // 按钮文本
  text = '';
  // 按钮精灵
  sprite = null;
  // 按钮状态
  isPressed = false;
  // 是否可见
  visible = true;
  // 是否启用
  enabled = true;
  // 按钮点击回调
  onClick = null;
  // 按钮样式 - 扁平化设计
  style = {
    // 基础样式
    variant: 'primary', // primary, secondary, outline
    backgroundColor: COLORS.primary[500],
    hoverColor: COLORS.primary[600],
    pressedColor: COLORS.primary[700],
    disabledColor: COLORS.neutral[300],

    // 文字样式
    textColor: '#ffffff',
    disabledTextColor: COLORS.neutral[500],
    fontSize: parseInt(TYPOGRAPHY.fontSize.base),
    fontWeight: TYPOGRAPHY.fontWeight.medium,
    fontFamily: TYPOGRAPHY.fontFamily.sans,

    // 边框和圆角
    borderRadius: parseInt(BORDER_RADIUS.md),
    borderWidth: 0,
    borderColor: 'transparent',

    // 阴影效果
    shadowColor: 'rgba(0, 0, 0, 0.1)',
    shadowBlur: 4,
    shadowOffsetX: 0,
    shadowOffsetY: 2,

    // 交互效果
    pressedScale: 0.98,
    hoverScale: 1.02,

    // 现代化效果
    gradient: false, // 扁平化设计不使用渐变
    glowEffect: false, // 扁平化设计不使用发光效果
    rippleEffect: true,
    animationDuration: 0.2
  };
  // 原始大小 (用于缩放动画)
  originalWidth = 0;
  originalHeight = 0;
  // 按钮动画状态
  animation = {
    isAnimating: false,
    scale: 1,
    targetScale: 1,
    speed: 0.15,
    ripples: [], // 波纹效果数组
    glowIntensity: 0,
    targetGlowIntensity: 0
  };

  // 鼠标状态
  mouseState = {
    isHover: false,
    isPressed: false,
    lastClickTime: 0
  };

  /**
   * 构造函数
   * @param {Object} options - 按钮选项
   * @param {number} options.x - 按钮X坐标
   * @param {number} options.y - 按钮Y坐标
   * @param {number} options.width - 按钮宽度
   * @param {number} options.height - 按钮高度
   * @param {string} options.text - 按钮文本
   * @param {string} options.image - 按钮图片路径
   * @param {Function} options.onClick - 点击回调函数
   * @param {Object} options.style - 按钮样式
   */
  constructor(options = {}) {
    // 设置按钮属性
    this.x = options.x || 0;
    this.y = options.y || 0;
    this.width = options.width || 100;
    this.height = options.height || 40;
    this.text = options.text || '';
    this.onClick = options.onClick || null;
    
    // 保存原始大小
    this.originalWidth = this.width;
    this.originalHeight = this.height;
    
    // 合并样式
    if (options.style) {
      this.style = { ...this.style, ...options.style };
    }
    
    // 如果提供了图片路径，创建精灵
    if (options.image) {
      this.sprite = new Sprite(
        options.image,
        this.width,
        this.height,
        this.x,
        this.y
      );
    }
  }
  
  /**
   * 设置按钮位置
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   */
  setPosition(x, y) {
    this.x = x;
    this.y = y;
    
    if (this.sprite) {
      this.sprite.x = x;
      this.sprite.y = y;
    }
  }
  
  /**
   * 设置按钮大小
   * @param {number} width - 宽度
   * @param {number} height - 高度
   */
  setSize(width, height) {
    this.width = width;
    this.height = height;
    this.originalWidth = width;
    this.originalHeight = height;
    
    if (this.sprite) {
      this.sprite.width = width;
      this.sprite.height = height;
    }
  }
  
  /**
   * 设置按钮文本
   * @param {string} text - 按钮文本
   */
  setText(text) {
    this.text = text;
  }
  
  /**
   * 设置按钮图片
   * @param {string} imagePath - 图片路径
   */
  setImage(imagePath) {
    if (!this.sprite) {
      this.sprite = new Sprite(
        imagePath,
        this.width,
        this.height,
        this.x,
        this.y
      );
    } else {
      this.sprite.img.src = imagePath;
    }
  }
  
  /**
   * 设置按钮可见性
   * @param {boolean} visible - 是否可见
   */
  setVisible(visible) {
    this.visible = visible;
    
    if (this.sprite) {
      this.sprite.visible = visible;
    }
  }
  
  /**
   * 设置按钮启用状态
   * @param {boolean} enabled - 是否启用
   */
  setEnabled(enabled) {
    this.enabled = enabled;
  }
  
  /**
   * 按下按钮
   */
  press() {
    if (!this.enabled) return;
    
    this.isPressed = true;
    this.animation.isAnimating = true;
    this.animation.targetScale = this.style.pressedScale;
    
    // 播放按钮音效
    this.playButtonSound();
  }
  
  /**
   * 释放按钮
   */
  release() {
    if (!this.enabled) return;
    
    this.isPressed = false;
    this.animation.isAnimating = true;
    this.animation.targetScale = 1;
    
    // 触发点击回调
    if (this.onClick) {
      this.onClick();
    }
  }
  
  /**
   * 播放按钮音效
   */
  playButtonSound() {
    try {
      if (!ASSETS.audio.buttonClick) return;

      // 浏览器环境兼容
      if (typeof wx !== 'undefined' && wx.createInnerAudioContext) {
        const audio = wx.createInnerAudioContext();
        audio.src = ASSETS.audio.buttonClick;
        audio.play();
      } else {
        // 浏览器环境使用HTML5 Audio
        const audio = new Audio(ASSETS.audio.buttonClick);
        audio.volume = 0.5;
        audio.play().catch(e => {
          console.log('播放音效失败:', e);
        });
      }
    } catch (e) {
      console.log('播放按钮音效失败:', e);
    }
  }
  
  /**
   * 更新按钮状态
   */
  update() {
    // 更新缩放动画
    if (this.animation.isAnimating) {
      const scaleDiff = this.animation.targetScale - this.animation.scale;
      if (Math.abs(scaleDiff) > 0.001) {
        this.animation.scale += scaleDiff * this.animation.speed;

        // 更新按钮大小
        this.width = this.originalWidth * this.animation.scale;
        this.height = this.originalHeight * this.animation.scale;

        // 调整位置以保持居中
        const offsetX = (this.originalWidth - this.width) / 2;
        const offsetY = (this.originalHeight - this.height) / 2;

        if (this.sprite) {
          this.sprite.width = this.width;
          this.sprite.height = this.height;
          this.sprite.x = this.x + offsetX;
          this.sprite.y = this.y + offsetY;
        }
      } else {
        this.animation.scale = this.animation.targetScale;
        this.animation.isAnimating = false;
      }
    }

    // 更新发光效果
    const glowDiff = this.animation.targetGlowIntensity - this.animation.glowIntensity;
    if (Math.abs(glowDiff) > 0.01) {
      this.animation.glowIntensity += glowDiff * this.animation.speed;
    }

    // 更新波纹效果
    this.updateRipples();
  }

  /**
   * 更新波纹效果
   */
  updateRipples() {
    this.animation.ripples = this.animation.ripples.filter(ripple => {
      ripple.radius += ripple.speed;
      ripple.opacity -= ripple.fadeSpeed;
      return ripple.opacity > 0 && ripple.radius < ripple.maxRadius;
    });
  }

  /**
   * 添加波纹效果
   * @param {number} x - 点击X坐标
   * @param {number} y - 点击Y坐标
   */
  addRipple(x, y) {
    if (!this.style.rippleEffect) return;

    const ripple = {
      x: x - this.x,
      y: y - this.y,
      radius: 0,
      maxRadius: Math.max(this.width, this.height) * 0.8,
      opacity: 0.3,
      speed: 4,
      fadeSpeed: 0.02
    };

    this.animation.ripples.push(ripple);
  }
  
  /**
   * 渲染按钮
   * @param {Object} ctx - Canvas上下文
   */
  render(ctx) {
    if (!this.visible) return;

    // 保存上下文
    ctx.save();

    // 渲染扁平化按钮
    this.renderFlatButton(ctx);

    // 恢复上下文
    ctx.restore();
  }

  /**
   * 渲染扁平化按钮
   */
  renderFlatButton(ctx) {
    // 计算当前状态的样式
    const currentStyle = this.getCurrentStyle();

    // 应用变换
    if (this.animation.currentScale !== 1) {
      ctx.translate(this.x + this.width / 2, this.y + this.height / 2);
      ctx.scale(this.animation.currentScale, this.animation.currentScale);
      ctx.translate(-this.width / 2, -this.height / 2);
    } else {
      ctx.translate(this.x, this.y);
    }

    // 绘制阴影
    if (currentStyle.shadowBlur > 0) {
      ctx.shadowColor = currentStyle.shadowColor;
      ctx.shadowBlur = currentStyle.shadowBlur;
      ctx.shadowOffsetX = currentStyle.shadowOffsetX;
      ctx.shadowOffsetY = currentStyle.shadowOffsetY;
    }

    // 绘制按钮背景
    ctx.beginPath();
    DESIGN_UTILS.drawRoundRect(ctx, 0, 0, this.width, this.height, currentStyle.borderRadius);
    ctx.fillStyle = currentStyle.backgroundColor;
    ctx.fill();

    // 绘制边框（如果有）
    if (currentStyle.borderWidth > 0) {
      ctx.shadowColor = 'transparent';
      ctx.strokeStyle = currentStyle.borderColor;
      ctx.lineWidth = currentStyle.borderWidth;
      ctx.stroke();
    }

    // 绘制文字
    ctx.shadowColor = 'transparent';
    ctx.fillStyle = currentStyle.textColor;
    ctx.font = `${currentStyle.fontWeight} ${currentStyle.fontSize}px ${currentStyle.fontFamily}`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(this.text, this.width / 2, this.height / 2);
  }

  /**
   * 获取当前状态的样式
   */
  getCurrentStyle() {
    const baseStyle = { ...this.style };

    if (!this.enabled) {
      baseStyle.backgroundColor = this.style.disabledColor;
      baseStyle.textColor = this.style.disabledTextColor;
      baseStyle.shadowBlur = 0;
    } else if (this.isPressed) {
      baseStyle.backgroundColor = this.style.pressedColor;
    } else if (this.isHovered) {
      baseStyle.backgroundColor = this.style.hoverColor;
    }

    return baseStyle;
  }
  
  /**
   * 检查点击是否命中按钮
   * @param {number} x - 点击X坐标
   * @param {number} y - 点击Y坐标
   * @returns {boolean} 是否命中
   */
  checkClick(x, y) {
    if (!this.visible || !this.enabled) return false;
    
    return (
      x >= this.x &&
      x <= this.x + this.width &&
      y >= this.y &&
      y <= this.y + this.height
    );
  }
  
  /**
   * 使颜色变暗
   * @param {string} color - 颜色字符串 (#RRGGBB)
   * @param {number} percent - 变暗百分比
   * @returns {string} 变暗后的颜色
   */
  darkenColor(color, percent) {
    // 解析颜色
    const r = parseInt(color.substr(1, 2), 16);
    const g = parseInt(color.substr(3, 2), 16);
    const b = parseInt(color.substr(5, 2), 16);
    
    // 变暗
    const factor = 1 - percent / 100;
    const newR = Math.floor(r * factor);
    const newG = Math.floor(g * factor);
    const newB = Math.floor(b * factor);
    
    // 转换回颜色字符串
    return `#${this.componentToHex(newR)}${this.componentToHex(newG)}${this.componentToHex(newB)}`;
  }
  
  /**
   * 使颜色变亮
   * @param {string} color - 颜色字符串 (#RRGGBB)
   * @param {number} percent - 变亮百分比
   * @returns {string} 变亮后的颜色
   */
  lightenColor(color, percent) {
    // 解析颜色
    const r = parseInt(color.substr(1, 2), 16);
    const g = parseInt(color.substr(3, 2), 16);
    const b = parseInt(color.substr(5, 2), 16);
    
    // 变亮
    const factor = percent / 100;
    const newR = Math.floor(r + (255 - r) * factor);
    const newG = Math.floor(g + (255 - g) * factor);
    const newB = Math.floor(b + (255 - b) * factor);
    
    // 转换回颜色字符串
    return `#${this.componentToHex(newR)}${this.componentToHex(newG)}${this.componentToHex(newB)}`;
  }
  
  /**
   * 将颜色分量转换为十六进制字符串
   * @param {number} c - 颜色分量 (0-255)
   * @returns {string} 十六进制字符串
   */
  componentToHex(c) {
    const hex = Math.max(0, Math.min(255, c)).toString(16);
    return hex.length === 1 ? '0' + hex : hex;
  }

  /**
   * 绘制圆角矩形
   * @param {CanvasRenderingContext2D} ctx - Canvas上下文
   * @param {number} x - 左上角X坐标
   * @param {number} y - 左上角Y坐标
   * @param {number} width - 宽度
   * @param {number} height - 高度
   * @param {number} radius - 圆角半径
   */
  drawRoundRect(ctx, x, y, width, height, radius) {
    if (width < 2 * radius) radius = width / 2;
    if (height < 2 * radius) radius = height / 2;
    
    ctx.beginPath();
    ctx.moveTo(x + radius, y);
    ctx.arcTo(x + width, y, x + width, y + height, radius);
    ctx.arcTo(x + width, y + height, x, y + height, radius);
    ctx.arcTo(x, y + height, x, y, radius);
    ctx.arcTo(x, y, x + width, y, radius);
    ctx.closePath();
  }

  /**
   * 渲染波纹效果
   * @param {Object} ctx - Canvas上下文
   */
  renderRipples(ctx) {
    if (!this.style.rippleEffect || this.animation.ripples.length === 0) return;

    ctx.save();

    // 设置裁剪区域为按钮形状
    ctx.beginPath();
    this.drawRoundRect(ctx, this.x, this.y, this.width, this.height, this.style.borderRadius);
    ctx.clip();

    // 绘制每个波纹
    this.animation.ripples.forEach(ripple => {
      ctx.beginPath();
      ctx.arc(this.x + ripple.x, this.y + ripple.y, ripple.radius, 0, Math.PI * 2);
      ctx.fillStyle = `rgba(255, 255, 255, ${ripple.opacity})`;
      ctx.fill();
    });

    ctx.restore();
  }

  /**
   * 渲染发光效果
   * @param {Object} ctx - Canvas上下文
   */
  renderGlow(ctx) {
    ctx.save();

    const glowSize = this.animation.glowIntensity * 20;
    const glowOpacity = this.animation.glowIntensity * 0.5;

    ctx.shadowColor = this.style.backgroundColor;
    ctx.shadowBlur = glowSize;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = 0;

    // 绘制发光轮廓
    ctx.beginPath();
    this.drawRoundRect(ctx, this.x - 2, this.y - 2, this.width + 4, this.height + 4, this.style.borderRadius + 2);
    ctx.strokeStyle = `rgba(255, 255, 255, ${glowOpacity})`;
    ctx.lineWidth = 2;
    ctx.stroke();

    ctx.restore();
  }

  /**
   * 处理触摸开始事件
   * @param {Object} touch - 触摸对象
   */
  onTouchStart(touch) {
    if (!this.visible || !this.enabled) return false;

    if (this.isPointInside(touch.clientX, touch.clientY)) {
      this.isPressed = true;
      this.mouseState.isPressed = true;
      this.mouseState.lastClickTime = Date.now();
      this.startPressAnimation();
      this.addRipple(touch.clientX, touch.clientY);
      this.playSound();
      return true;
    }

    return false;
  }

  /**
   * 处理触摸结束事件
   * @param {Object} touch - 触摸对象
   */
  onTouchEnd(touch) {
    if (this.isPressed) {
      this.isPressed = false;
      this.mouseState.isPressed = false;
      this.endPressAnimation();

      if (this.isPointInside(touch.clientX, touch.clientY) && this.onClick) {
        this.onClick();
      }

      return true;
    }

    return false;
  }

  /**
   * 处理鼠标移动事件
   * @param {Object} event - 鼠标事件
   */
  onMouseMove(event) {
    if (!this.visible || !this.enabled) return false;

    const wasHover = this.mouseState.isHover;
    this.mouseState.isHover = this.isPointInside(event.clientX, event.clientY);

    // 悬停状态改变时的处理
    if (this.mouseState.isHover !== wasHover) {
      if (this.mouseState.isHover) {
        this.onMouseEnter();
      } else {
        this.onMouseLeave();
      }
    }

    return this.mouseState.isHover;
  }

  /**
   * 鼠标进入处理
   */
  onMouseEnter() {
    this.animation.targetScale = this.style.hoverScale || 1.02;
    this.animation.targetGlowIntensity = 1;
    this.animation.isAnimating = true;
  }

  /**
   * 鼠标离开处理
   */
  onMouseLeave() {
    this.animation.targetScale = 1;
    this.animation.targetGlowIntensity = 0;
    this.animation.isAnimating = true;
    this.mouseState.isPressed = false;
  }

  /**
   * 开始按压动画
   */
  startPressAnimation() {
    this.animation.targetScale = this.style.pressedScale || 0.96;
    this.animation.isAnimating = true;
  }

  /**
   * 结束按压动画
   */
  endPressAnimation() {
    this.animation.targetScale = this.mouseState.isHover ? (this.style.hoverScale || 1.02) : 1;
    this.animation.isAnimating = true;
  }

  /**
   * 播放按钮音效
   */
  playSound() {
    try {
      if (window.audioManager && window.audioManager.isEnabled) {
        window.audioManager.playEffect('button_click');
      }
    } catch (e) {
      console.log('播放按钮音效失败:', e);
    }
  }
}