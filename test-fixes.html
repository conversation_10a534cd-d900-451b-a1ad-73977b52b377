<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>修复测试 - 飞行棋游戏</title>
    <!-- 解决SharedArrayBuffer跨域隔离警告 -->
    <meta http-equiv="Cross-Origin-Opener-Policy" content="same-origin">
    <meta http-equiv="Cross-Origin-Embedder-Policy" content="require-corp">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        #gameCanvas {
            border: 1px solid #ccc;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>飞行棋游戏修复测试</h1>
    
    <div class="test-section">
        <h2>1. 环境检测测试</h2>
        <button onclick="testEnvironment()">测试环境检测</button>
        <div id="environmentResult"></div>
    </div>
    
    <div class="test-section">
        <h2>2. Canvas获取测试</h2>
        <canvas id="gameCanvas" width="400" height="300"></canvas>
        <br>
        <button onclick="testCanvas()">测试Canvas获取</button>
        <div id="canvasResult"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 错误处理测试</h2>
        <button onclick="testErrorHandling()">测试错误处理</button>
        <div id="errorResult"></div>
    </div>
    
    <div class="test-section">
        <h2>4. 微信小游戏API兼容性测试</h2>
        <button onclick="testWeChatAPI()">测试微信API兼容</button>
        <div id="wechatResult"></div>
    </div>
    
    <div class="test-section">
        <h2>5. 微信小游戏兼容性测试</h2>
        <button onclick="testWeChatCompatibility()">测试微信兼容性</button>
        <div id="wechatCompatResult"></div>
    </div>

    <div class="test-section">
        <h2>6. 游戏启动测试</h2>
        <button onclick="testGameStart()">测试游戏启动</button>
        <div id="gameResult"></div>
    </div>

    <script type="module">
        // 设置全局变量
        window.canvas = document.getElementById('gameCanvas');
        window.SCREEN_WIDTH = 400;
        window.SCREEN_HEIGHT = 300;
        window.GameGlobal = {
            databus: null,
            pool: null,
            currentScene: null
        };

        // 测试函数
        window.testEnvironment = async function() {
            const resultDiv = document.getElementById('environmentResult');
            try {
                const { environmentDetector } = await import('./js/utils/environment.js');
                
                const envType = environmentDetector.getEnvironmentType();
                const capabilities = environmentDetector.getCapabilities();
                
                let html = `<div class="test-result success">
                    <strong>环境检测成功</strong><br>
                    环境类型: ${envType}<br>
                    支持Canvas: ${capabilities.hasCanvas}<br>
                    支持Document: ${capabilities.hasDocument}<br>
                    支持Window: ${capabilities.hasWindow}<br>
                    支持音频: ${capabilities.hasAudio}<br>
                    支持本地存储: ${capabilities.hasLocalStorage}<br>
                    支持触摸: ${capabilities.hasTouch}
                </div>`;
                
                resultDiv.innerHTML = html;
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-result error">
                    <strong>环境检测失败:</strong> ${error.message}
                </div>`;
            }
        };

        window.testCanvas = async function() {
            const resultDiv = document.getElementById('canvasResult');
            try {
                const { getCanvas } = await import('./js/utils/environment.js');
                
                const canvas = getCanvas();
                const ctx = canvas.getContext('2d');
                
                // 测试绘制
                ctx.fillStyle = '#4CAF50';
                ctx.fillRect(10, 10, 100, 50);
                ctx.fillStyle = '#2196F3';
                ctx.fillText('测试成功', 20, 35);
                
                resultDiv.innerHTML = `<div class="test-result success">
                    <strong>Canvas获取成功</strong><br>
                    Canvas尺寸: ${canvas.width} x ${canvas.height}<br>
                    已在Canvas上绘制测试图形
                </div>`;
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-result error">
                    <strong>Canvas获取失败:</strong> ${error.message}
                </div>`;
            }
        };

        window.testErrorHandling = async function() {
            const resultDiv = document.getElementById('errorResult');
            try {
                const { errorHandler, handleError, safeExecute, ERROR_TYPES, ERROR_LEVELS } = await import('./js/utils/errorHandler.js');
                
                // 测试安全执行
                const result1 = safeExecute(() => {
                    return "正常执行成功";
                }, "降级值", ERROR_TYPES.UNKNOWN);
                
                // 测试错误处理
                const result2 = safeExecute(() => {
                    throw new Error("测试错误");
                }, "降级值", ERROR_TYPES.UNKNOWN);
                
                // 测试错误记录
                handleError({
                    type: ERROR_TYPES.GAME_LOGIC,
                    level: ERROR_LEVELS.WARNING,
                    message: "这是一个测试警告"
                });
                
                const errorLog = errorHandler.getErrorLog();
                
                resultDiv.innerHTML = `<div class="test-result success">
                    <strong>错误处理测试成功</strong><br>
                    正常执行结果: ${result1}<br>
                    错误执行结果: ${result2}<br>
                    错误日志数量: ${errorLog.length}
                </div>`;
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-result error">
                    <strong>错误处理测试失败:</strong> ${error.message}
                </div>`;
            }
        };

        window.testWeChatAPI = function() {
            const resultDiv = document.getElementById('wechatResult');
            try {
                // 检查微信API兼容层
                const hasWx = typeof wx !== 'undefined';
                const hasCreateCanvas = hasWx && typeof wx.createCanvas === 'function';
                const hasGetWindowInfo = hasWx && typeof wx.getWindowInfo === 'function';
                const hasOnError = hasWx && typeof wx.onError === 'function';
                
                let status = 'success';
                let message = '微信API兼容层正常';
                
                if (!hasWx) {
                    status = 'warning';
                    message = '当前环境不是微信小游戏，已创建兼容层';
                }
                
                resultDiv.innerHTML = `<div class="test-result ${status}">
                    <strong>${message}</strong><br>
                    wx对象存在: ${hasWx}<br>
                    wx.createCanvas: ${hasCreateCanvas}<br>
                    wx.getWindowInfo: ${hasGetWindowInfo}<br>
                    wx.onError: ${hasOnError}
                </div>`;
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-result error">
                    <strong>微信API测试失败:</strong> ${error.message}
                </div>`;
            }
        };

        window.testWeChatCompatibility = async function() {
            const resultDiv = document.getElementById('wechatCompatResult');
            try {
                const { wechatCompat, getCompatibilityStatus } = await import('./js/utils/wechatCompat.js');

                const status = getCompatibilityStatus();

                let statusClass = 'success';
                if (!status.initialized) {
                    statusClass = 'warning';
                }

                resultDiv.innerHTML = `<div class="test-result ${statusClass}">
                    <strong>微信兼容性测试</strong><br>
                    是否微信环境: ${status.isWeChatGame}<br>
                    兼容性已初始化: ${status.initialized}<br>
                    SharedArrayBuffer警告: ${status.hasSharedArrayBufferWarning ? '存在（正常）' : '无'}<br>
                    已启用的补丁: ${Object.keys(status.polyfills).join(', ') || '无'}
                </div>`;
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-result error">
                    <strong>微信兼容性测试失败:</strong> ${error.message}
                </div>`;
            }
        };

        window.testGameStart = async function() {
            const resultDiv = document.getElementById('gameResult');
            try {
                // 尝试导入并创建游戏实例
                const { default: Main } = await import('./js/main.js');

                // 创建游戏实例（但不启动循环）
                const gameInstance = new Main();

                // 停止游戏循环以避免干扰
                if (gameInstance.aniId) {
                    cancelAnimationFrame(gameInstance.aniId);
                }

                resultDiv.innerHTML = `<div class="test-result success">
                    <strong>游戏启动测试成功</strong><br>
                    游戏实例已创建<br>
                    当前场景: ${gameInstance.currentScene ? gameInstance.currentScene.name : '无'}<br>
                    场景数量: ${Object.keys(gameInstance.scenes).length}
                </div>`;
            } catch (error) {
                resultDiv.innerHTML = `<div class="test-result error">
                    <strong>游戏启动测试失败:</strong> ${error.message}<br>
                    <small>错误堆栈: ${error.stack}</small>
                </div>`;
            }
        };

        // 页面加载完成后自动运行基础测试
        window.addEventListener('load', () => {
            console.log('页面加载完成，开始基础测试...');
            testEnvironment();
            testWeChatAPI();
        });
    </script>
</body>
</html>
