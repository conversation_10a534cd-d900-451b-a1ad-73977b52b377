import { BOARD_CONFIG, BOARD_LAYOUT, PLAYER_COLORS, ASSETS } from '../config/gameConfig.js';
import { SCREEN_WIDTH, SCREEN_HEIGHT } from '../render.js';
import Sprite from '../base/sprite.js';

/**
 * 棋盘类
 * 负责绘制棋盘和管理格子位置
 */
export default class Board {
  // 棋盘格子
  cells = [];
  // 基地区域
  baseAreas = {};
  // 安全跑道
  safeRunways = {};
  // 终点区域
  finishArea = null;
  // 缩放比例
  scale = 1;
  // 棋盘背景
  background = null;
  // 格子大小
  cellSize = BOARD_LAYOUT.cellSize;
  // 是否使用Canvas绘制
  useCanvas = false;

  constructor() {
    // 计算缩放比例
    this.calculateScale();

    // 加载棋盘背景
    this.loadBackground();

    // 初始化棋盘格子
    this.initCells();

    // 初始化基地区域
    this.initBaseAreas();

    // 初始化安全跑道
    this.initSafeRunways();

    // 初始化终点区域
    this.initFinishArea();

    // 初始化交互状态
    this.highlightedPositions = [];
    this.selectedPiece = null;
    this.hoveredCell = null;

    // 性能优化相关
    this.needsRedraw = true;
    this.lastRenderTime = 0;
    this.renderCache = null;
    this.cacheValid = false;

    // 创建离屏画布用于缓存
    this.createOffscreenCanvas();
  }
  
  /**
   * 计算缩放比例和布局参数
   */
  calculateScale() {
    // 设计基准尺寸
    const designWidth = 800;
    const designHeight = 600;

    // 计算缩放比例
    const scaleX = SCREEN_WIDTH / designWidth;
    const scaleY = SCREEN_HEIGHT / designHeight;
    this.scale = Math.min(scaleX, scaleY, 1);

    // 计算棋盘布局参数
    this.layout = this.calculateBoardLayout();

    // 设置棋盘区域信息
    this.boardArea = {
      centerX: this.layout.centerX,
      centerY: this.layout.centerY,
      width: this.layout.boardSize,
      height: this.layout.boardSize
    };
  }

  /**
   * 计算棋盘布局参数 - 响应式设计
   */
  calculateBoardLayout() {
    // 响应式布局计算
    const aspectRatio = SCREEN_WIDTH / SCREEN_HEIGHT;
    const isLandscape = aspectRatio > 1.2;
    const isPortrait = aspectRatio < 0.8;

    // 根据屏幕方向调整布局
    let availableWidth, availableHeight, centerX, centerY;

    if (isLandscape) {
      // 横屏模式 - 棋盘占据中央区域，留出侧边空间
      availableWidth = SCREEN_HEIGHT * 0.8; // 基于高度计算
      availableHeight = SCREEN_HEIGHT * 0.8;
      centerX = SCREEN_WIDTH / 2;
      centerY = SCREEN_HEIGHT / 2;
    } else if (isPortrait) {
      // 竖屏模式 - 棋盘占据更多垂直空间
      availableWidth = SCREEN_WIDTH * 0.9;
      availableHeight = SCREEN_WIDTH * 0.9; // 基于宽度计算
      centerX = SCREEN_WIDTH / 2;
      centerY = SCREEN_HEIGHT / 2;
    } else {
      // 标准模式
      const minDimension = Math.min(SCREEN_WIDTH, SCREEN_HEIGHT);
      availableWidth = minDimension * 0.7;
      availableHeight = minDimension * 0.7;
      centerX = SCREEN_WIDTH / 2;
      centerY = SCREEN_HEIGHT / 2;
    }

    // 计算格子大小 - 响应式
    const baseCellSize = Math.min(availableWidth, availableHeight) / 15;
    const cellSize = Math.max(baseCellSize, 20); // 最小20像素，最大不超过40像素
    const finalCellSize = Math.min(cellSize, 40);

    // 计算各区域的尺寸
    const crossWidth = finalCellSize * 6;
    const crossHeight = finalCellSize * 15;
    const baseSize = finalCellSize * 5; // 稍微减小基地大小

    // 确保整个布局适合屏幕
    const totalWidth = crossWidth + baseSize * 2;
    const totalHeight = crossHeight + baseSize * 2;

    // 如果布局太大，进行缩放
    const scaleX = availableWidth / totalWidth;
    const scaleY = availableHeight / totalHeight;
    const layoutScale = Math.min(scaleX, scaleY, 1);

    const scaledCellSize = finalCellSize * layoutScale;
    const scaledCrossWidth = crossWidth * layoutScale;
    const scaledCrossHeight = crossHeight * layoutScale;
    const scaledBaseSize = baseSize * layoutScale;

    return {
      centerX,
      centerY,
      boardSize: Math.min(availableWidth, availableHeight),
      cellSize: scaledCellSize,
      crossWidth: scaledCrossWidth,
      crossHeight: scaledCrossHeight,
      baseSize: scaledBaseSize,
      layoutScale,
      isLandscape,
      isPortrait,
      // 基地区域位置（四个角落）
      bases: {
        red: {
          x: centerX - scaledCrossWidth/2 - scaledBaseSize/2,
          y: centerY - scaledCrossHeight/2 - scaledBaseSize/2
        },
        yellow: {
          x: centerX + scaledCrossWidth/2 + scaledBaseSize/2,
          y: centerY - scaledCrossHeight/2 - scaledBaseSize/2
        },
        blue: {
          x: centerX + scaledCrossWidth/2 + scaledBaseSize/2,
          y: centerY + scaledCrossHeight/2 + scaledBaseSize/2
        },
        green: {
          x: centerX - scaledCrossWidth/2 - scaledBaseSize/2,
          y: centerY + scaledCrossHeight/2 + scaledBaseSize/2
        }
      }
    };
  }
  
  /**
   * 加载棋盘背景
   */
  loadBackground() {
    try {
      if (ASSETS.boardBackground && ASSETS.boardBackground.trim() !== '') {
        this.background = new Sprite(
          ASSETS.boardBackground,
          SCREEN_WIDTH,
          SCREEN_HEIGHT,
          0,
          0
        );
      } else {
        this.useCanvas = true;
      }
    } catch (error) {
      console.warn('棋盘背景加载失败，使用Canvas绘制:', error);
      this.useCanvas = true;
    }
  }
  
  /**
   * 初始化棋盘格子 - 标准52格飞行棋布局
   */
  initCells() {
    this.cells = [];

    // 创建标准飞行棋52格布局
    this.createStandardFlightChessLayout();
  }

  /**
   * 创建标准飞行棋52格布局
   */
  createStandardFlightChessLayout() {
    const { centerX, centerY, cellSize, crossWidth, crossHeight } = this.layout;

    // 外圈52个格子的坐标
    const outerCells = [];

    // 计算十字形的四个边
    const leftX = centerX - crossWidth / 2;
    const rightX = centerX + crossWidth / 2;
    const topY = centerY - crossHeight / 2;
    const bottomY = centerY + crossHeight / 2;
    const middleX = centerX;
    const middleY = centerY;

    // 左侧垂直线（从上到下）- 格子1-6
    for (let i = 0; i < 6; i++) {
      outerCells.push({
        id: i + 1,
        x: leftX,
        y: topY + i * cellSize,
        type: this.getCellType(i + 1),
        color: this.getCellColor(i + 1)
      });
    }

    // 底部水平线（从左到右）- 格子7-18
    for (let i = 0; i < 12; i++) {
      outerCells.push({
        id: i + 7,
        x: leftX + i * cellSize,
        y: bottomY,
        type: this.getCellType(i + 7),
        color: this.getCellColor(i + 7)
      });
    }

    // 右侧垂直线（从下到上）- 格子19-24
    for (let i = 0; i < 6; i++) {
      outerCells.push({
        id: i + 19,
        x: rightX,
        y: bottomY - i * cellSize,
        type: this.getCellType(i + 19),
        color: this.getCellColor(i + 19)
      });
    }

    // 顶部水平线（从右到左）- 格子25-36
    for (let i = 0; i < 12; i++) {
      outerCells.push({
        id: i + 25,
        x: rightX - i * cellSize,
        y: topY,
        type: this.getCellType(i + 25),
        color: this.getCellColor(i + 25)
      });
    }

    // 左侧垂直线（从上到下，继续）- 格子37-42
    for (let i = 6; i < 12; i++) {
      outerCells.push({
        id: i + 31,
        x: leftX,
        y: topY + i * cellSize,
        type: this.getCellType(i + 31),
        color: this.getCellColor(i + 31)
      });
    }

    // 中间水平线（从左到右）- 格子43-52
    for (let i = 1; i < 11; i++) {
      outerCells.push({
        id: i + 42,
        x: leftX + i * cellSize,
        y: middleY,
        type: this.getCellType(i + 42),
        color: this.getCellColor(i + 42)
      });
    }

    this.cells = outerCells;
  }

  /**
   * 获取格子类型
   */
  getCellType(cellId) {
    // 起点格子
    if ([1, 14, 27, 40].includes(cellId)) {
      return 'start';
    }

    // 安全格子
    if ([9, 22, 35, 48].includes(cellId)) {
      return 'safe';
    }

    // 飞行格子
    if ([5, 18, 31, 44].includes(cellId)) {
      return 'fly';
    }

    return 'normal';
  }

  /**
   * 获取格子颜色
   */
  getCellColor(cellId) {
    // 红色玩家区域
    if (cellId >= 1 && cellId <= 13) {
      return PLAYER_COLORS.RED;
    }

    // 黄色玩家区域
    if (cellId >= 14 && cellId <= 26) {
      return PLAYER_COLORS.YELLOW;
    }

    // 蓝色玩家区域
    if (cellId >= 27 && cellId <= 39) {
      return PLAYER_COLORS.BLUE;
    }

    // 绿色玩家区域
    if (cellId >= 40 && cellId <= 52) {
      return PLAYER_COLORS.GREEN;
    }

    return null;
  }

  /**
   * 创建十字形棋盘布局
   * @param {number} centerX - 中心X坐标
   * @param {number} centerY - 中心Y坐标
   * @param {number} radius - 半径
   */
  createCrossLayout(centerX, centerY, radius) {
    const cellsPerSide = 13; // 每边13个格子
    const totalCells = 52; // 总共52个格子

    // 计算四个边的起始位置
    const sides = [
      { startAngle: -Math.PI / 2, name: 'top' },    // 上边
      { startAngle: 0, name: 'right' },             // 右边
      { startAngle: Math.PI / 2, name: 'bottom' },  // 下边
      { startAngle: Math.PI, name: 'left' }         // 左边
    ];

    let cellIndex = 0;

    sides.forEach((side, sideIndex) => {
      for (let i = 0; i < cellsPerSide; i++) {
        const progress = i / (cellsPerSide - 1);
        let x, y;

        // 根据边的方向计算位置
        switch (side.name) {
          case 'top':
            x = centerX - radius + (progress * radius * 2);
            y = centerY - radius;
            break;
          case 'right':
            x = centerX + radius;
            y = centerY - radius + (progress * radius * 2);
            break;
          case 'bottom':
            x = centerX + radius - (progress * radius * 2);
            y = centerY + radius;
            break;
          case 'left':
            x = centerX - radius;
            y = centerY + radius - (progress * radius * 2);
            break;
        }

        const cell = {
          id: cellIndex,
          x: x,
          y: y,
          width: this.cellSize,
          height: this.cellSize,
          color: this.getCellColor(cellIndex),
          type: this.getCellType(cellIndex),
          isSpecial: this.isSpecialCell(cellIndex)
        };

        this.cells.push(cell);
        cellIndex++;
      }
    });
  }
  
  /**
   * 获取格子颜色
   */
  getCellColor(cellIndex) {
    // 检查是否是特殊颜色格子
    for (const [color, positions] of Object.entries(BOARD_CONFIG.coloredCells)) {
      if (positions.includes(cellIndex)) {
        return color;
      }
    }

    return 'white'; // 默认白色
  }

  /**
   * 获取格子类型
   * @param {number} cellIndex - 格子索引
   * @returns {string} 格子类型
   */
  getCellType(cellIndex) {
    // 检查是否是起点
    for (const [color, position] of Object.entries(BOARD_CONFIG.startPoints)) {
      if (position === cellIndex) {
        return 'start';
      }
    }

    // 检查是否是飞行格子
    for (const [color, positions] of Object.entries(BOARD_CONFIG.flyingCells)) {
      if (positions.includes(cellIndex)) {
        return 'flying';
      }
    }

    // 检查是否是彩色格子
    for (const [color, positions] of Object.entries(BOARD_CONFIG.coloredCells)) {
      if (positions.includes(cellIndex)) {
        return 'colored';
      }
    }

    return 'normal'; // 默认普通格子
  }
  
  /**
   * 检查是否是特殊格子
   */
  isSpecialCell(cellIndex) {
    // 检查是否是飞行格子
    for (const positions of Object.values(BOARD_CONFIG.flyingCells)) {
      if (positions.includes(cellIndex)) {
        return true;
      }
    }
    
    // 检查是否是起点
    for (const startPoint of Object.values(BOARD_CONFIG.startPoints)) {
      if (startPoint === cellIndex) {
        return true;
      }
    }
    
    return false;
  }
  
  /**
   * 初始化基地区域
   */
  initBaseAreas() {
    this.baseAreas = {};

    const { bases, baseSize } = this.layout;

    // 为每个玩家创建基地区域
    Object.entries(bases).forEach(([colorKey, position]) => {
      const color = PLAYER_COLORS[colorKey.toUpperCase()];
      this.baseAreas[color] = {
        x: position.x,
        y: position.y,
        width: baseSize,
        height: baseSize,
        color: color,
        pieces: [],
        // 基地内的4个棋子位置
        piecePositions: this.calculateBasePiecePositions(position.x, position.y, baseSize)
      };
    });
  }

  /**
   * 计算基地内棋子位置
   */
  calculateBasePiecePositions(baseX, baseY, baseSize) {
    const positions = [];
    const pieceSize = baseSize / 3;
    const spacing = baseSize / 4;

    // 2x2 排列
    for (let row = 0; row < 2; row++) {
      for (let col = 0; col < 2; col++) {
        positions.push({
          x: baseX + spacing + col * (pieceSize + spacing/2),
          y: baseY + spacing + row * (pieceSize + spacing/2)
        });
      }
    }

    return positions;
  }

  /**
   * 计算基地内棋子位置（旧方法，保持兼容性）
   */
  calculateBasePositions(color) {
    const basePositions = BOARD_LAYOUT.basePositions[color];
    return basePositions.map(pos => ({
      x: pos.x * this.scale,
      y: pos.y * this.scale
    }));
  }
  
  /**
   * 初始化安全跑道
   */
  initSafeRunways() {
    this.safeRunways = {};
    
    const centerX = SCREEN_WIDTH / 2;
    const centerY = SCREEN_HEIGHT / 2;
    
    // 为每种颜色创建安全跑道
    const colors = Object.keys(PLAYER_COLORS);
    colors.forEach((colorKey, index) => {
      const color = PLAYER_COLORS[colorKey];
      const positions = [];
      
      // 计算安全跑道位置（从外圈指向中心）
      const angle = (index / colors.length) * Math.PI * 2;
      const startRadius = 150 * this.scale;
      const endRadius = 50 * this.scale;
      
      for (let i = 0; i < BOARD_CONFIG.safeRunwayLength; i++) {
        const progress = i / (BOARD_CONFIG.safeRunwayLength - 1);
        const radius = startRadius - (startRadius - endRadius) * progress;
        
        positions.push({
          x: centerX + Math.cos(angle) * radius,
          y: centerY + Math.sin(angle) * radius,
          width: this.cellSize,
          height: this.cellSize
        });
      }
      
      this.safeRunways[color] = positions;
    });
  }
  
  /**
   * 初始化终点区域
   */
  initFinishArea() {
    this.finishArea = {
      x: this.boardArea.centerX,
      y: this.boardArea.centerY,
      radius: Math.max(this.cellSize * 1.5, 40) // 终点区域半径
    };
  }
  
  /**
   * 获取棋子在棋盘上的位置
   * @param {Object} piece - 棋子对象
   * @returns {Object} 位置坐标
   */
  getPiecePosition(piece) {
    try {
      // 在基地
      if (piece.status === PIECE_STATUS.BASE) {
        const baseArea = this.baseAreas[piece.color];
        if (baseArea && baseArea.positions && baseArea.positions[piece.index]) {
          return {
            x: baseArea.positions[piece.index].x,
            y: baseArea.positions[piece.index].y
          };
        }
        // 如果没有预设位置，计算默认位置
        return this.calculateDefaultBasePosition(piece);
      }

      // 在跑道上
      if (piece.status === PIECE_STATUS.RUNWAY) {
        const cell = this.cells[piece.position];
        if (cell) {
          return {
            x: cell.x + cell.width / 2,
            y: cell.y + cell.height / 2
          };
        }
        return this.getDefaultPosition();
      }

      // 在安全跑道
      if (piece.status === PIECE_STATUS.SAFE) {
        const safeRunway = this.safeRunways[piece.color];
        if (safeRunway && safeRunway.length > 0) {
          const safeIndex = Math.min(piece.position - 52, safeRunway.length - 1);
          const safeCell = safeRunway[safeIndex];
          if (safeCell) {
            return {
              x: safeCell.x + this.cellSize / 2,
              y: safeCell.y + this.cellSize / 2
            };
          }
        }
        return this.getDefaultPosition();
      }

      // 已完成
      if (piece.status === PIECE_STATUS.FINISHED) {
        return this.calculateFinishedPosition(piece);
      }

      return this.getDefaultPosition();
    } catch (error) {
      console.warn('获取棋子位置失败:', error);
      return this.getDefaultPosition();
    }
  }

  /**
   * 计算基地默认位置
   * @param {Object} piece - 棋子对象
   * @returns {Object} 位置坐标
   */
  calculateDefaultBasePosition(piece) {
    const baseArea = this.baseAreas[piece.color];
    if (!baseArea) return this.getDefaultPosition();

    // 在基地中心周围排列
    const angle = (piece.index / 4) * Math.PI * 2;
    const radius = baseArea.radius * 0.4;

    return {
      x: baseArea.x + Math.cos(angle) * radius,
      y: baseArea.y + Math.sin(angle) * radius
    };
  }

  /**
   * 计算完成区域位置
   * @param {Object} piece - 棋子对象
   * @returns {Object} 位置坐标
   */
  calculateFinishedPosition(piece) {
    // 在终点区域中心周围排列已完成的棋子
    const finishedPieces = this.getFinishedPiecesCount(piece.color);
    const angle = (finishedPieces / 4) * Math.PI * 2;
    const radius = this.finishArea.radius * 0.3;

    return {
      x: this.finishArea.x + Math.cos(angle) * radius,
      y: this.finishArea.y + Math.sin(angle) * radius
    };
  }

  /**
   * 获取已完成棋子数量
   * @param {string} color - 棋子颜色
   * @returns {number} 已完成棋子数量
   */
  getFinishedPiecesCount(color) {
    // 这里需要从游戏数据中获取，暂时返回0
    return 0;
  }

  /**
   * 获取默认位置
   * @returns {Object} 默认位置坐标
   */
  getDefaultPosition() {
    return {
      x: this.boardArea ? this.boardArea.centerX : SCREEN_WIDTH / 2,
      y: this.boardArea ? this.boardArea.centerY : SCREEN_HEIGHT / 2
    };
  }
  
  /**
   * 检查点击位置
   * @param {number} x - 点击X坐标
   * @param {number} y - 点击Y坐标
   * @returns {Object} 点击信息
   */
  checkClick(x, y) {
    try {
      // 检查是否点击了棋盘格子（优先级最高）
      for (let i = 0; i < this.cells.length; i++) {
        const cell = this.cells[i];
        if (this.isPointInCell(x, y, cell)) {
          return {
            type: 'cell',
            cellIndex: i,
            cell: cell,
            priority: 3
          };
        }
      }

      // 检查是否点击了安全跑道
      for (const [color, runway] of Object.entries(this.safeRunways)) {
        for (let i = 0; i < runway.length; i++) {
          const cell = runway[i];
          if (this.isPointInCell(x, y, cell)) {
            return {
              type: 'safe',
              color: color,
              index: i,
              cell: cell,
              priority: 2
            };
          }
        }
      }

      // 检查是否点击了基地区域
      for (const [color, baseArea] of Object.entries(this.baseAreas)) {
        if (this.isPointInCircle(x, y, baseArea.x, baseArea.y, baseArea.radius)) {
          return {
            type: 'base',
            color: color,
            baseArea: baseArea,
            priority: 1
          };
        }
      }

      // 检查是否点击了终点区域
      if (this.finishArea && this.isPointInCircle(x, y, this.finishArea.x, this.finishArea.y, this.finishArea.radius)) {
        return {
          type: 'finish',
          area: this.finishArea,
          priority: 1
        };
      }

      return { type: 'none', priority: 0 };
    } catch (error) {
      console.warn('点击检测失败:', error);
      return { type: 'none', priority: 0 };
    }
  }
  
  /**
   * 检查点是否在格子内
   */
  isPointInCell(x, y, cell) {
    return x >= cell.x - cell.width / 2 &&
           x <= cell.x + cell.width / 2 &&
           y >= cell.y - cell.height / 2 &&
           y <= cell.y + cell.height / 2;
  }
  
  /**
   * 检查点是否在圆形区域内
   */
  isPointInCircle(x, y, centerX, centerY, radius) {
    const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);
    return distance <= radius;
  }
  

  
  /**
   * 使用Canvas绘制棋盘
   */
  renderWithCanvas(ctx) {
    // 绘制背景
    ctx.fillStyle = '#F5F5DC'; // 米色背景
    ctx.fillRect(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT);
    
    // 绘制棋盘格子
    this.renderCells(ctx);
    
    // 绘制基地区域
    this.renderBaseAreas(ctx);
    
    // 绘制安全跑道
    this.renderSafeRunways(ctx);
    
    // 绘制终点区域
    this.renderFinishArea(ctx);
  }
  


  
  /**
   * 加载棋盘背景
   */
  loadBackground() {
    try {
      this.background = new Sprite(
        ASSETS.boardBackground,
        SCREEN_WIDTH,
        SCREEN_HEIGHT,
        0,
        0
      );
    } catch (e) {
      console.log('无法加载棋盘背景图片，将使用Canvas绘制', e);
      this.useCanvas = true;
    }
  }

  
  /**
   * 初始化基地区域
   */
  initBaseAreas() {
    this.baseAreas = {};

    // 计算基地区域的位置和大小
    const centerX = this.boardArea.centerX;
    const centerY = this.boardArea.centerY;
    const baseRadius = Math.max(this.cellSize * 2.5, 60); // 基地半径
    const baseDistance = Math.min(this.boardArea.width, this.boardArea.height) * 0.25; // 基地距离中心的距离

    // 四个基地的位置（左上、右上、右下、左下）
    const basePositions = {
      'red': { x: centerX - baseDistance, y: centerY - baseDistance },
      'yellow': { x: centerX + baseDistance, y: centerY - baseDistance },
      'blue': { x: centerX + baseDistance, y: centerY + baseDistance },
      'green': { x: centerX - baseDistance, y: centerY + baseDistance }
    };

    // 创建基地区域
    Object.entries(basePositions).forEach(([color, pos]) => {
      this.baseAreas[color] = {
        x: pos.x,
        y: pos.y,
        radius: baseRadius,
        color: color,
        positions: this.calculateBasePositions(pos.x, pos.y, baseRadius, color)
      };
    });
  }

  /**
   * 计算基地内棋子的位置
   * @param {number} centerX - 基地中心X坐标
   * @param {number} centerY - 基地中心Y坐标
   * @param {number} radius - 基地半径
   * @param {string} color - 颜色
   * @returns {Array} 棋子位置数组
   */
  calculateBasePositions(centerX, centerY, radius, color) {
    const positions = [];
    const pieceRadius = this.cellSize * 0.3;
    const spacing = radius * 0.6;

    // 2x2排列
    const offsets = [
      { x: -spacing / 2, y: -spacing / 2 },
      { x: spacing / 2, y: -spacing / 2 },
      { x: -spacing / 2, y: spacing / 2 },
      { x: spacing / 2, y: spacing / 2 }
    ];

    offsets.forEach(offset => {
      positions.push({
        x: centerX + offset.x,
        y: centerY + offset.y
      });
    });

    return positions;
  }
  
  /**
   * 初始化安全跑道
   */
  initSafeRunways() {
    this.safeRunways = {};
    
    // 计算棋盘中心点
    const centerX = BOARD_LAYOUT.centerX * this.scale;
    const centerY = BOARD_LAYOUT.centerY * this.scale;
    
    // 为每种颜色创建安全跑道
    Object.entries(BOARD_CONFIG.startPoints).forEach(([color, startPoint]) => {
      const runway = [];
      
      // 获取起点格子的角度
      const startCell = this.cells[startPoint];
      const startAngle = Math.atan2(
        startCell.y + this.cellSize / 2 - centerY,
        startCell.x + this.cellSize / 2 - centerX
      );
      
      // 创建6个安全跑道格子
      for (let i = 0; i < BOARD_CONFIG.safeRunwayLength; i++) {
        // 计算格子到中心的距离 (从外向内)
        const distance = (5 - i) * this.cellSize;
        
        // 计算格子坐标
        const x = centerX + distance * Math.cos(startAngle) - this.cellSize / 2;
        const y = centerY + distance * Math.sin(startAngle) - this.cellSize / 2;
        
        // 添加安全跑道格子
        runway.push({
          index: i,
          x,
          y,
          type: 'safe',
          color,
          pieces: [] // 当前格子上的棋子
        });
      }
      
      this.safeRunways[color] = runway;
    });
  }
  
  /**
   * 初始化终点区域
   */
  initFinishArea() {
    // 计算棋盘中心点
    const centerX = BOARD_LAYOUT.centerX * this.scale;
    const centerY = BOARD_LAYOUT.centerY * this.scale;
    
    // 创建终点区域
    this.finishArea = {
      x: centerX - this.cellSize / 2,
      y: centerY - this.cellSize / 2,
      width: this.cellSize,
      height: this.cellSize,
      pieces: [] // 到达终点的棋子
    };
  }
  
  /**
   * 获取棋子在基地的初始位置
   * @param {string} color - 棋子颜色
   * @param {number} index - 棋子索引
   * @returns {Object} 位置坐标
   */
  getPieceBasePosition(color, index) {
    const basePositions = BOARD_LAYOUT.basePositions[color];
    if (!basePositions || index >= basePositions.length) {
      return null;
    }
    
    return {
      x: basePositions[index].x * this.scale,
      y: basePositions[index].y * this.scale
    };
  }
  
  /**
   * 获取指定位置的格子
   * @param {number} position - 格子位置
   * @returns {Object} 格子对象
   */
  getCellByPosition(position) {
    if (position < 0 || position >= this.cells.length) {
      return null;
    }
    return this.cells[position];
  }
  
  /**
   * 获取安全跑道格子
   * @param {string} color - 棋子颜色
   * @param {number} index - 安全跑道索引
   * @returns {Object} 格子对象
   */
  getSafeRunwayCell(color, index) {
    const runway = this.safeRunways[color];
    if (!runway || index < 0 || index >= runway.length) {
      return null;
    }
    return runway[index];
  }
  
  /**
   * 添加棋子到格子
   * @param {Object} piece - 棋子对象
   * @param {number} position - 格子位置
   */
  addPieceToCell(piece, position) {
    const cell = this.getCellByPosition(position);
    if (cell) {
      cell.pieces.push(piece);
    }
  }
  
  /**
   * 从格子移除棋子
   * @param {Object} piece - 棋子对象
   * @param {number} position - 格子位置
   */
  removePieceFromCell(piece, position) {
    const cell = this.getCellByPosition(position);
    if (cell) {
      cell.pieces = cell.pieces.filter(p => p.id !== piece.id);
    }
  }
  
  /**
   * 添加棋子到安全跑道
   * @param {Object} piece - 棋子对象
   * @param {string} color - 棋子颜色
   * @param {number} index - 安全跑道索引
   */
  addPieceToSafeRunway(piece, color, index) {
    const cell = this.getSafeRunwayCell(color, index);
    if (cell) {
      cell.pieces.push(piece);
    }
  }
  
  /**
   * 从安全跑道移除棋子
   * @param {Object} piece - 棋子对象
   * @param {string} color - 棋子颜色
   * @param {number} index - 安全跑道索引
   */
  removePieceFromSafeRunway(piece, color, index) {
    const cell = this.getSafeRunwayCell(color, index);
    if (cell) {
      cell.pieces = cell.pieces.filter(p => p.id !== piece.id);
    }
  }
  
  /**
   * 添加棋子到终点
   * @param {Object} piece - 棋子对象
   */
  addPieceToFinish(piece) {
    this.finishArea.pieces.push(piece);
  }
  
  /**
   * 使用Canvas绘制棋盘背景
   * @param {Object} ctx - Canvas上下文
   */
  drawBackground(ctx) {
    // 保存上下文
    ctx.save();

    // 创建径向渐变背景
    const centerX = SCREEN_WIDTH / 2;
    const centerY = SCREEN_HEIGHT / 2;
    const radius = Math.max(SCREEN_WIDTH, SCREEN_HEIGHT) * 0.8;

    const gradient = ctx.createRadialGradient(
      centerX, centerY, 0,
      centerX, centerY, radius
    );
    gradient.addColorStop(0, '#f8f9fa');
    gradient.addColorStop(0.3, '#e9ecef');
    gradient.addColorStop(0.7, '#dee2e6');
    gradient.addColorStop(1, '#ced4da');

    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT);

    // 添加纹理效果
    this.drawBackgroundTexture(ctx);

    // 绘制装饰性边框
    this.drawDecorativeBorder(ctx);

    // 绘制标题
    this.drawTitle(ctx);

    // 恢复上下文
    ctx.restore();
  }

  /**
   * 绘制背景纹理
   * @param {Object} ctx - Canvas上下文
   */
  drawBackgroundTexture(ctx) {
    ctx.save();

    // 创建细微的点状纹理
    ctx.globalAlpha = 0.1;
    ctx.fillStyle = '#6c757d';

    for (let x = 0; x < SCREEN_WIDTH; x += 20) {
      for (let y = 0; y < SCREEN_HEIGHT; y += 20) {
        if (Math.random() > 0.7) {
          ctx.beginPath();
          ctx.arc(x, y, 1, 0, Math.PI * 2);
          ctx.fill();
        }
      }
    }

    ctx.restore();
  }

  /**
   * 绘制装饰性边框
   * @param {Object} ctx - Canvas上下文
   */
  drawDecorativeBorder(ctx) {
    ctx.save();

    const margin = 15;
    const borderRadius = 20;

    // 外边框阴影
    ctx.shadowColor = 'rgba(0, 0, 0, 0.2)';
    ctx.shadowBlur = 15;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = 5;

    // 绘制圆角矩形边框
    ctx.strokeStyle = '#495057';
    ctx.lineWidth = 3;
    ctx.beginPath();
    this.drawRoundRect(ctx, margin, margin, SCREEN_WIDTH - margin * 2, SCREEN_HEIGHT - margin * 2, borderRadius);
    ctx.stroke();

    // 内边框高光
    ctx.shadowColor = 'transparent';
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)';
    ctx.lineWidth = 1;
    ctx.beginPath();
    this.drawRoundRect(ctx, margin + 2, margin + 2, SCREEN_WIDTH - (margin + 2) * 2, SCREEN_HEIGHT - (margin + 2) * 2, borderRadius - 2);
    ctx.stroke();

    ctx.restore();
  }

  /**
   * 绘制标题
   * @param {Object} ctx - Canvas上下文
   */
  drawTitle(ctx) {
    ctx.save();

    const titleY = 50;

    // 标题阴影
    ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
    ctx.shadowBlur = 8;
    ctx.shadowOffsetX = 2;
    ctx.shadowOffsetY = 2;

    // 主标题
    ctx.fillStyle = '#2c3e50';
    ctx.font = 'bold 32px "Microsoft YaHei", Arial, sans-serif';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText('飞行棋', SCREEN_WIDTH / 2, titleY);

    // 标题装饰线
    ctx.shadowColor = 'transparent';
    const lineY = titleY + 25;
    const lineWidth = 120;
    const lineX = SCREEN_WIDTH / 2 - lineWidth / 2;

    // 渐变装饰线
    const lineGradient = ctx.createLinearGradient(lineX, lineY, lineX + lineWidth, lineY);
    lineGradient.addColorStop(0, 'transparent');
    lineGradient.addColorStop(0.2, '#3498db');
    lineGradient.addColorStop(0.5, '#2980b9');
    lineGradient.addColorStop(0.8, '#3498db');
    lineGradient.addColorStop(1, 'transparent');

    ctx.strokeStyle = lineGradient;
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(lineX, lineY);
    ctx.lineTo(lineX + lineWidth, lineY);
    ctx.stroke();

    ctx.restore();
  }

  /**
   * 绘制圆角矩形
   * @param {Object} ctx - Canvas上下文
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {number} width - 宽度
   * @param {number} height - 高度
   * @param {number} radius - 圆角半径
   */
  drawRoundRect(ctx, x, y, width, height, radius) {
    ctx.moveTo(x + radius, y);
    ctx.lineTo(x + width - radius, y);
    ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
    ctx.lineTo(x + width, y + height - radius);
    ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
    ctx.lineTo(x + radius, y + height);
    ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
    ctx.lineTo(x, y + radius);
    ctx.quadraticCurveTo(x, y, x + radius, y);
  }

  /**
   * 获取增强的格子颜色
   * @param {string} color - 基础颜色
   * @returns {Object} 包含浅色、主色、深色的颜色对象
   */
  getEnhancedCellColor(color) {
    const colorMap = {
      'red': {
        light: '#ffebee',
        main: '#f44336',
        dark: '#c62828'
      },
      'yellow': {
        light: '#fffde7',
        main: '#ffeb3b',
        dark: '#f57f17'
      },
      'blue': {
        light: '#e3f2fd',
        main: '#2196f3',
        dark: '#1565c0'
      },
      'green': {
        light: '#e8f5e8',
        main: '#4caf50',
        dark: '#2e7d32'
      }
    };

    return colorMap[color] || {
      light: '#f5f5f5',
      main: '#9e9e9e',
      dark: '#616161'
    };
  }

  /**
   * 绘制起点标记
   * @param {Object} ctx - Canvas上下文
   * @param {Object} cell - 格子对象
   */
  drawStartMarker(ctx, cell) {
    ctx.save();

    const centerX = cell.x + this.cellSize / 2;
    const centerY = cell.y + this.cellSize / 2;
    const size = this.cellSize * 0.3;

    // 绘制箭头
    ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
    ctx.strokeStyle = 'rgba(0, 0, 0, 0.7)';
    ctx.lineWidth = 1;

    ctx.beginPath();
    ctx.moveTo(centerX - size / 2, centerY + size / 3);
    ctx.lineTo(centerX + size / 2, centerY);
    ctx.lineTo(centerX - size / 2, centerY - size / 3);
    ctx.lineTo(centerX - size / 4, centerY);
    ctx.closePath();
    ctx.fill();
    ctx.stroke();

    ctx.restore();
  }

  /**
   * 绘制飞行格子标记
   * @param {Object} ctx - Canvas上下文
   * @param {Object} cell - 格子对象
   */
  drawFlyingMarker(ctx, cell) {
    ctx.save();

    const centerX = cell.x + this.cellSize / 2;
    const centerY = cell.y + this.cellSize / 2;
    const size = this.cellSize * 0.25;

    // 绘制星星
    ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
    ctx.strokeStyle = 'rgba(255, 215, 0, 0.8)';
    ctx.lineWidth = 1;

    this.drawStar(ctx, centerX, centerY, 5, size, size * 0.5);
    ctx.fill();
    ctx.stroke();

    ctx.restore();
  }

  /**
   * 绘制星星
   * @param {Object} ctx - Canvas上下文
   * @param {number} cx - 中心X坐标
   * @param {number} cy - 中心Y坐标
   * @param {number} spikes - 星星尖角数量
   * @param {number} outerRadius - 外半径
   * @param {number} innerRadius - 内半径
   */
  drawStar(ctx, cx, cy, spikes, outerRadius, innerRadius) {
    let rot = Math.PI / 2 * 3;
    let x = cx;
    let y = cy;
    const step = Math.PI / spikes;

    ctx.beginPath();
    ctx.moveTo(cx, cy - outerRadius);

    for (let i = 0; i < spikes; i++) {
      x = cx + Math.cos(rot) * outerRadius;
      y = cy + Math.sin(rot) * outerRadius;
      ctx.lineTo(x, y);
      rot += step;

      x = cx + Math.cos(rot) * innerRadius;
      y = cy + Math.sin(rot) * innerRadius;
      ctx.lineTo(x, y);
      rot += step;
    }

    ctx.lineTo(cx, cy - outerRadius);
    ctx.closePath();
  }
  
  /**
   * 渲染棋盘
   * @param {Object} ctx - Canvas上下文
   */
  render(ctx) {
    // 使用优化渲染
    if (this.offscreenCanvas) {
      this.renderOptimized(ctx);
    } else {
      // 降级到普通渲染
      this.renderNormal(ctx);
      this.renderHighlight(ctx);
      this.renderHoverEffect(ctx);
    }
  }
  
  /**
   * 渲染基地区域
   * @param {Object} ctx - Canvas上下文
   */
  renderBaseAreas(ctx) {
    Object.values(this.baseAreas).forEach(base => {
      ctx.save();

      // 绘制基地阴影
      ctx.shadowColor = 'rgba(0, 0, 0, 0.2)';
      ctx.shadowBlur = 10;
      ctx.shadowOffsetX = 2;
      ctx.shadowOffsetY = 4;

      // 绘制基地背景
      ctx.beginPath();
      ctx.arc(base.x, base.y, base.radius, 0, 2 * Math.PI);

      // 创建径向渐变
      const gradient = ctx.createRadialGradient(
        base.x - base.radius * 0.3, base.y - base.radius * 0.3, 0,
        base.x, base.y, base.radius
      );

      const colors = this.getEnhancedCellColor(base.color);
      gradient.addColorStop(0, colors.light);
      gradient.addColorStop(0.6, colors.main + '40'); // 添加透明度
      gradient.addColorStop(1, colors.dark + '20');

      ctx.fillStyle = gradient;
      ctx.fill();

      // 重置阴影
      ctx.shadowColor = 'transparent';

      // 绘制基地边框
      ctx.strokeStyle = colors.dark;
      ctx.lineWidth = 3;
      ctx.stroke();

      // 绘制内圈装饰
      ctx.beginPath();
      ctx.arc(base.x, base.y, base.radius * 0.8, 0, 2 * Math.PI);
      ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)';
      ctx.lineWidth = 1;
      ctx.stroke();

      // 绘制基地标识
      ctx.fillStyle = colors.dark;
      ctx.font = `bold ${Math.floor(base.radius * 0.2)}px "Microsoft YaHei", Arial, sans-serif`;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText('基地', base.x, base.y);

      ctx.restore();
    });
  }
  
  /**
   * 渲染主跑道格子
   * @param {Object} ctx - Canvas上下文
   */
  renderCells(ctx) {
    this.cells.forEach((cell, index) => {
      ctx.save();

      // 绘制格子阴影
      ctx.shadowColor = 'rgba(0, 0, 0, 0.15)';
      ctx.shadowBlur = 4;
      ctx.shadowOffsetX = 1;
      ctx.shadowOffsetY = 2;

      // 绘制格子背景
      ctx.beginPath();
      this.drawRoundRect(ctx, cell.x - 2, cell.y - 2, this.cellSize + 4, this.cellSize + 4, 6);

      // 根据格子类型设置颜色和样式
      if (cell.type === 'normal') {
        ctx.fillStyle = '#ffffff';
      } else if (cell.type === 'colored' || cell.type === 'start') {
        // 为彩色格子创建渐变效果
        const gradient = ctx.createLinearGradient(
          cell.x, cell.y,
          cell.x + this.cellSize, cell.y + this.cellSize
        );
        const baseColor = this.getEnhancedCellColor(cell.color);
        gradient.addColorStop(0, baseColor.light);
        gradient.addColorStop(0.5, baseColor.main);
        gradient.addColorStop(1, baseColor.dark);
        ctx.fillStyle = gradient;
      } else if (cell.type === 'flying') {
        // 飞行格子使用特殊的星空效果
        const gradient = ctx.createRadialGradient(
          cell.x + this.cellSize / 2, cell.y + this.cellSize / 2, 0,
          cell.x + this.cellSize / 2, cell.y + this.cellSize / 2, this.cellSize / 2
        );
        gradient.addColorStop(0, '#ffd700');
        gradient.addColorStop(0.6, '#ffb347');
        gradient.addColorStop(1, '#ff8c00');
        ctx.fillStyle = gradient;
      }

      ctx.fill();

      // 重置阴影
      ctx.shadowColor = 'transparent';

      // 绘制格子边框
      ctx.strokeStyle = cell.type === 'normal' ? '#e9ecef' : 'rgba(255, 255, 255, 0.8)';
      ctx.lineWidth = cell.type === 'start' ? 2 : 1;
      ctx.stroke();

      // 为起点格子添加特殊标记
      if (cell.type === 'start') {
        this.drawStartMarker(ctx, cell);
      }

      // 为飞行格子添加星星效果
      if (cell.type === 'flying') {
        this.drawFlyingMarker(ctx, cell);
      }

      // 绘制格子编号（调试用，可选）
      if (false) { // 设置为true可显示格子编号
        ctx.fillStyle = '#666';
        ctx.font = '10px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(
          index.toString(),
          cell.x + this.cellSize / 2,
          cell.y + this.cellSize / 2
        );
      }

      ctx.restore();
      
      // 如果是飞行格子，绘制飞机图标
      if (cell.type === 'flying') {
        this.drawFlyingIcon(ctx, cell);
      }
    });
  }
  
  /**
   * 绘制起点箭头
   * @param {Object} ctx - Canvas上下文
   * @param {Object} cell - 格子对象
   */
  drawStartArrow(ctx, cell) {
    const centerX = cell.x + this.cellSize / 2;
    const centerY = cell.y + this.cellSize / 2;
    const arrowSize = this.cellSize * 0.4;
    
    ctx.beginPath();
    ctx.moveTo(centerX - arrowSize / 2, centerY - arrowSize / 2);
    ctx.lineTo(centerX + arrowSize / 2, centerY);
    ctx.lineTo(centerX - arrowSize / 2, centerY + arrowSize / 2);
    ctx.closePath();
    
    ctx.fillStyle = '#FFFFFF';
    ctx.fill();
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 1;
    ctx.stroke();
  }
  
  /**
   * 绘制飞行图标
   * @param {Object} ctx - Canvas上下文
   * @param {Object} cell - 格子对象
   */
  drawFlyingIcon(ctx, cell) {
    const centerX = cell.x + this.cellSize / 2;
    const centerY = cell.y + this.cellSize / 2;
    const iconSize = this.cellSize * 0.5;
    
    // 简单绘制飞机图标
    ctx.beginPath();
    ctx.moveTo(centerX, centerY - iconSize / 2);
    ctx.lineTo(centerX + iconSize / 3, centerY);
    ctx.lineTo(centerX, centerY + iconSize / 2);
    ctx.lineTo(centerX - iconSize / 3, centerY);
    ctx.closePath();
    
    ctx.fillStyle = '#000000';
    ctx.fill();
  }
  
  /**
   * 渲染安全跑道
   * @param {Object} ctx - Canvas上下文
   */
  renderSafeRunways(ctx) {
    Object.entries(this.safeRunways).forEach(([color, runway]) => {
      runway.forEach((cell, index) => {
        ctx.save();

        // 绘制安全跑道阴影
        ctx.shadowColor = 'rgba(0, 0, 0, 0.1)';
        ctx.shadowBlur = 3;
        ctx.shadowOffsetX = 1;
        ctx.shadowOffsetY = 1;

        // 绘制安全跑道格子
        ctx.beginPath();
        this.drawRoundRect(ctx, cell.x - 1, cell.y - 1, this.cellSize + 2, this.cellSize + 2, 4);

        // 创建安全跑道的特殊渐变效果
        const colors = this.getEnhancedCellColor(color);
        const gradient = ctx.createLinearGradient(
          cell.x, cell.y,
          cell.x + this.cellSize, cell.y + this.cellSize
        );
        gradient.addColorStop(0, colors.light + 'CC'); // 80% 透明度
        gradient.addColorStop(0.5, colors.main + '99'); // 60% 透明度
        gradient.addColorStop(1, colors.dark + '66'); // 40% 透明度

        ctx.fillStyle = gradient;
        ctx.fill();

        // 重置阴影
        ctx.shadowColor = 'transparent';

        // 绘制安全跑道边框
        ctx.strokeStyle = colors.dark;
        ctx.lineWidth = 2;
        ctx.stroke();

        // 绘制安全标记（盾牌图标）
        this.drawSafetyShield(ctx, cell.x + this.cellSize / 2, cell.y + this.cellSize / 2, this.cellSize * 0.3);

        // 绘制跑道编号
        ctx.fillStyle = colors.dark;
        ctx.font = `bold ${Math.floor(this.cellSize * 0.2)}px Arial`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'bottom';
        ctx.fillText(
          (index + 1).toString(),
          cell.x + this.cellSize / 2,
          cell.y + this.cellSize - 2
        );

        ctx.restore();
      });
    });
  }

  /**
   * 绘制安全标记（盾牌）
   * @param {Object} ctx - Canvas上下文
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {number} size - 尺寸
   */
  drawSafetyShield(ctx, x, y, size) {
    ctx.save();

    // 绘制盾牌
    ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
    ctx.strokeStyle = 'rgba(0, 0, 0, 0.6)';
    ctx.lineWidth = 1;

    ctx.beginPath();
    ctx.moveTo(x, y - size * 0.5);
    ctx.quadraticCurveTo(x - size * 0.4, y - size * 0.3, x - size * 0.4, y);
    ctx.quadraticCurveTo(x - size * 0.4, y + size * 0.3, x, y + size * 0.5);
    ctx.quadraticCurveTo(x + size * 0.4, y + size * 0.3, x + size * 0.4, y);
    ctx.quadraticCurveTo(x + size * 0.4, y - size * 0.3, x, y - size * 0.5);
    ctx.closePath();
    ctx.fill();
    ctx.stroke();

    // 绘制盾牌内的对勾
    ctx.strokeStyle = 'rgba(76, 175, 80, 0.8)';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(x - size * 0.2, y);
    ctx.lineTo(x - size * 0.05, y + size * 0.15);
    ctx.lineTo(x + size * 0.2, y - size * 0.1);
    ctx.stroke();

    ctx.restore();
  }
  
  /**
   * 渲染终点区域
   * @param {Object} ctx - Canvas上下文
   */
  renderFinishArea(ctx) {
    // 绘制终点区域
    ctx.beginPath();
    ctx.rect(
      this.finishArea.x,
      this.finishArea.y,
      this.finishArea.width,
      this.finishArea.height
    );
    
    // 使用彩色渐变
    const gradient = ctx.createRadialGradient(
      this.finishArea.x + this.cellSize / 2,
      this.finishArea.y + this.cellSize / 2,
      0,
      this.finishArea.x + this.cellSize / 2,
      this.finishArea.y + this.cellSize / 2,
      this.cellSize / 2
    );
    
    gradient.addColorStop(0, '#FFFFFF');
    gradient.addColorStop(0.25, PLAYER_COLORS.RED);
    gradient.addColorStop(0.5, PLAYER_COLORS.YELLOW);
    gradient.addColorStop(0.75, PLAYER_COLORS.BLUE);
    gradient.addColorStop(1, PLAYER_COLORS.GREEN);
    
    ctx.fillStyle = gradient;
    ctx.fill();
    
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 2;
    ctx.stroke();
    
    // 绘制"终点"文字
    ctx.fillStyle = '#FFFFFF';
    ctx.font = `bold ${this.cellSize * 0.4}px Arial`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(
      '终点',
      this.finishArea.x + this.cellSize / 2,
      this.finishArea.y + this.cellSize / 2
    );
  }
  
  /**
   * 检查点击位置是否在格子内
   * @param {number} x - 点击X坐标
   * @param {number} y - 点击Y坐标
   * @returns {Object} 点击的格子
   */
  checkCellClick(x, y) {
    // 检查主跑道格子
    for (const cell of this.cells) {
      if (
        x >= cell.x &&
        x <= cell.x + this.cellSize &&
        y >= cell.y &&
        y <= cell.y + this.cellSize
      ) {
        return { type: 'main', cell };
      }
    }
    
    // 检查安全跑道格子
    for (const [color, runway] of Object.entries(this.safeRunways)) {
      for (const cell of runway) {
        if (
          x >= cell.x &&
          x <= cell.x + this.cellSize &&
          y >= cell.y &&
          y <= cell.y + this.cellSize
        ) {
          return { type: 'safe', color, cell };
        }
      }
    }
    
    // 检查终点区域
    if (
      x >= this.finishArea.x &&
      x <= this.finishArea.x + this.finishArea.width &&
      y >= this.finishArea.y &&
      y <= this.finishArea.y + this.finishArea.height
    ) {
      return { type: 'finish', cell: this.finishArea };
    }
    
    // 检查基地区域
    for (const [color, base] of Object.entries(this.baseAreas)) {
      const distance = Math.sqrt(
        Math.pow(x - base.x, 2) + Math.pow(y - base.y, 2)
      );
      if (distance <= base.radius) {
        return { type: 'base', color, cell: base };
      }
    }
    
    return null;
  }

  /**
   * 高亮显示可移动的位置
   * @param {Array} positions - 位置数组
   */
  highlightPositions(positions) {
    this.highlightedPositions = positions || [];
  }

  /**
   * 清除高亮显示
   */
  clearHighlight() {
    this.highlightedPositions = [];
    this.hoveredCell = null;
  }

  /**
   * 设置悬停的格子
   * @param {Object} cell - 格子对象
   */
  setHoveredCell(cell) {
    this.hoveredCell = cell;
  }

  /**
   * 渲染高亮效果
   * @param {Object} ctx - Canvas上下文
   */
  renderHighlight(ctx) {
    if (!this.highlightedPositions || this.highlightedPositions.length === 0) return;

    ctx.save();

    const time = Date.now() / 1000;
    const pulse = Math.sin(time * 3) * 0.3 + 0.7;

    this.highlightedPositions.forEach(pos => {
      ctx.beginPath();
      ctx.arc(pos.x, pos.y, this.cellSize * 0.6 * pulse, 0, Math.PI * 2);
      ctx.fillStyle = `rgba(255, 215, 0, ${0.4 * pulse})`;
      ctx.fill();

      ctx.strokeStyle = `rgba(255, 165, 0, ${0.8 * pulse})`;
      ctx.lineWidth = 3;
      ctx.stroke();
    });

    ctx.restore();
  }

  /**
   * 渲染悬停效果
   * @param {Object} ctx - Canvas上下文
   */
  renderHoverEffect(ctx) {
    if (!this.hoveredCell) return;

    ctx.save();

    const time = Date.now() / 1000;
    const glow = Math.sin(time * 4) * 0.2 + 0.8;

    // 绘制悬停发光效果
    ctx.shadowColor = 'rgba(74, 144, 226, 0.6)';
    ctx.shadowBlur = 15 * glow;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = 0;

    ctx.beginPath();
    this.drawRoundRect(
      ctx,
      this.hoveredCell.x - this.cellSize / 2 - 2,
      this.hoveredCell.y - this.cellSize / 2 - 2,
      this.cellSize + 4,
      this.cellSize + 4,
      6
    );
    ctx.strokeStyle = `rgba(74, 144, 226, ${glow})`;
    ctx.lineWidth = 2;
    ctx.stroke();

    ctx.restore();
  }

  /**
   * 更新交互状态
   * @param {number} mouseX - 鼠标X坐标
   * @param {number} mouseY - 鼠标Y坐标
   */
  updateInteraction(mouseX, mouseY) {
    // 检查鼠标悬停
    let hoveredCell = null;

    for (let i = 0; i < this.cells.length; i++) {
      const cell = this.cells[i];
      if (this.isPointInCell(mouseX, mouseY, cell)) {
        hoveredCell = cell;
        break;
      }
    }

    this.setHoveredCell(hoveredCell);
  }

  /**
   * 创建离屏画布用于缓存
   */
  createOffscreenCanvas() {
    try {
      this.offscreenCanvas = document.createElement('canvas');
      this.offscreenCanvas.width = SCREEN_WIDTH;
      this.offscreenCanvas.height = SCREEN_HEIGHT;
      this.offscreenCtx = this.offscreenCanvas.getContext('2d');
    } catch (error) {
      console.warn('创建离屏画布失败:', error);
      this.offscreenCanvas = null;
      this.offscreenCtx = null;
    }
  }

  /**
   * 标记需要重绘
   */
  markForRedraw() {
    this.needsRedraw = true;
    this.cacheValid = false;
  }

  /**
   * 渲染到缓存
   */
  renderToCache() {
    if (!this.offscreenCtx) return;

    // 清空离屏画布
    this.offscreenCtx.clearRect(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT);

    // 绘制静态元素到缓存
    if (this.useCanvas) {
      this.drawBackground(this.offscreenCtx);
    } else if (this.background) {
      this.background.render(this.offscreenCtx);
    }

    this.renderBaseAreas(this.offscreenCtx);
    this.renderCells(this.offscreenCtx);
    this.renderSafeRunways(this.offscreenCtx);
    this.renderFinishArea(this.offscreenCtx);

    this.cacheValid = true;
  }

  /**
   * 优化后的渲染方法
   * @param {Object} ctx - Canvas上下文
   */
  renderOptimized(ctx) {
    const currentTime = Date.now();

    // 限制渲染频率（60fps）
    if (currentTime - this.lastRenderTime < 16) {
      return;
    }

    // 如果缓存无效，重新渲染到缓存
    if (!this.cacheValid && this.offscreenCanvas) {
      this.renderToCache();
    }

    // 使用缓存渲染静态部分
    if (this.cacheValid && this.offscreenCanvas) {
      ctx.drawImage(this.offscreenCanvas, 0, 0);
    } else {
      // 降级到普通渲染
      this.renderNormal(ctx);
    }

    // 渲染动态元素
    this.renderHighlight(ctx);
    this.renderHoverEffect(ctx);

    this.lastRenderTime = currentTime;
    this.needsRedraw = false;
  }

  /**
   * 普通渲染方法（降级方案）
   * @param {Object} ctx - Canvas上下文
   */
  renderNormal(ctx) {
    if (this.useCanvas) {
      this.drawBackground(ctx);
    } else if (this.background) {
      this.background.render(ctx);
    }

    this.renderBaseAreas(ctx);
    this.renderCells(ctx);
    this.renderSafeRunways(ctx);
    this.renderFinishArea(ctx);
  }

  /**
   * 检查是否需要重绘
   * @returns {boolean} 是否需要重绘
   */
  shouldRedraw() {
    return this.needsRedraw ||
           this.highlightedPositions.length > 0 ||
           this.hoveredCell !== null;
  }

  /**
   * 更新方法（用于动画）
   */
  update() {
    // 检查是否有动画需要更新
    const hasAnimations = this.highlightedPositions.length > 0 || this.hoveredCell !== null;

    if (hasAnimations) {
      this.markForRedraw();
    }
  }
}