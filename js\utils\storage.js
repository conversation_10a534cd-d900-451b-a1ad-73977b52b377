/**
 * 存储管理器
 * 负责游戏数据的本地存储和读取
 */
export default class Storage {
  /**
   * 存储数据
   * @param {string} key - 存储键名
   * @param {any} value - 存储的值
   */
  setItem(key, value) {
    try {
      // 浏览器环境兼容
      if (typeof wx !== 'undefined' && wx.setStorageSync) {
        wx.setStorageSync(`flychess_${key}`, JSON.stringify(value));
      } else {
        // 浏览器环境使用localStorage
        localStorage.setItem(`flychess_${key}`, JSON.stringify(value));
      }
      return true;
    } catch (e) {
      console.error('存储数据失败:', e);
      return false;
    }
  }

  /**
   * 获取数据
   * @param {string} key - 存储键名
   * @returns {any} 存储的值
   */
  getItem(key) {
    try {
      let value;
      // 浏览器环境兼容
      if (typeof wx !== 'undefined' && wx.getStorageSync) {
        value = wx.getStorageSync(`flychess_${key}`);
      } else {
        // 浏览器环境使用localStorage
        value = localStorage.getItem(`flychess_${key}`);
      }
      return value ? JSON.parse(value) : null;
    } catch (e) {
      console.error('获取数据失败:', e);
      return null;
    }
  }

  /**
   * 删除数据
   * @param {string} key - 存储键名
   */
  removeItem(key) {
    try {
      // 浏览器环境兼容
      if (typeof wx !== 'undefined' && wx.removeStorageSync) {
        wx.removeStorageSync(`flychess_${key}`);
      } else {
        // 浏览器环境使用localStorage
        localStorage.removeItem(`flychess_${key}`);
      }
      return true;
    } catch (e) {
      console.error('删除数据失败:', e);
      return false;
    }
  }

  /**
   * 清空所有数据
   */
  clear() {
    try {
      // 浏览器环境兼容
      if (typeof wx !== 'undefined' && wx.getStorageInfoSync) {
        // 微信小游戏环境
        const info = wx.getStorageInfoSync();
        const keys = info.keys.filter(key => key.startsWith('flychess_'));
        keys.forEach(key => wx.removeStorageSync(key));
      } else {
        // 浏览器环境
        const keys = [];
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && key.startsWith('flychess_')) {
            keys.push(key);
          }
        }
        keys.forEach(key => localStorage.removeItem(key));
      }
      return true;
    } catch (e) {
      console.error('清空数据失败:', e);
      return false;
    }
  }

  /**
   * 保存游戏进度
   * @param {Object} gameState - 游戏状态
   */
  saveGameProgress(gameState) {
    return this.setItem('gameProgress', gameState);
  }

  /**
   * 加载游戏进度
   * @returns {Object} 游戏状态
   */
  loadGameProgress() {
    return this.getItem('gameProgress');
  }

  /**
   * 判断是否有保存的游戏
   * @returns {boolean} 是否有保存的游戏
   */
  hasSavedGame() {
    return this.getItem('gameProgress') !== null;
  }
} 