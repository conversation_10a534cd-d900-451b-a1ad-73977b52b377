/**
 * 场景基类
 * 负责管理场景的生命周期和交互
 */
export default class Scene {
  // 场景是否激活
  active = false;
  // 场景名称
  name = 'base';
  // 场景元素
  elements = [];
  // 场景事件监听器
  eventListeners = {};
  // 父场景
  parent = null;
  // 子场景
  children = [];
  
  /**
   * 构造函数
   * @param {string} name - 场景名称
   */
  constructor(name = 'base') {
    this.name = name;
  }
  
  /**
   * 初始化场景
   * 在场景首次激活时调用
   */
  init() {
    this.active = true;
    
    // 初始化子场景
    this.children.forEach(child => {
      if (child.active) {
        child.init();
      }
    });
  }
  
  /**
   * 进入场景
   * 在场景每次激活时调用
   */
  enter() {
    this.active = true;
    
    // 注册触摸事件
    this.registerTouchEvents();
    
    // 进入子场景
    this.children.forEach(child => {
      if (child.active) {
        child.enter();
      }
    });
  }
  
  /**
   * 离开场景
   * 在场景每次停用时调用
   */
  exit() {
    this.active = false;
    
    // 注销触摸事件
    this.unregisterTouchEvents();
    
    // 离开子场景
    this.children.forEach(child => {
      if (child.active) {
        child.exit();
      }
    });
  }
  
  /**
   * 更新场景
   * 在游戏循环中调用
   */
  update() {
    if (!this.active) return;
    
    // 更新场景元素
    this.elements.forEach(element => {
      if (element.update) {
        element.update();
      }
    });
    
    // 更新子场景
    this.children.forEach(child => {
      if (child.active) {
        child.update();
      }
    });
  }
  
  /**
   * 渲染场景
   * @param {Object} ctx - Canvas上下文
   */
  render(ctx) {
    if (!this.active) return;
    
    // 渲染场景元素
    this.elements.forEach(element => {
      if (element.visible !== false && element.render) {
        element.render(ctx);
      }
    });
    
    // 渲染子场景
    this.children.forEach(child => {
      if (child.active) {
        child.render(ctx);
      }
    });
  }
  
  /**
   * 添加场景元素
   * @param {Object} element - 场景元素
   */
  addElement(element) {
    this.elements.push(element);
  }
  
  /**
   * 移除场景元素
   * @param {Object} element - 场景元素
   */
  removeElement(element) {
    const index = this.elements.indexOf(element);
    if (index !== -1) {
      this.elements.splice(index, 1);
    }
  }
  
  /**
   * 添加子场景
   * @param {Scene} child - 子场景
   */
  addChild(child) {
    child.parent = this;
    this.children.push(child);
  }
  
  /**
   * 移除子场景
   * @param {Scene} child - 子场景
   */
  removeChild(child) {
    const index = this.children.indexOf(child);
    if (index !== -1) {
      this.children[index].parent = null;
      this.children.splice(index, 1);
    }
  }
  
  /**
   * 注册触摸事件
   */
  registerTouchEvents() {
    // 浏览器环境兼容
    if (typeof wx !== 'undefined' && wx.onTouchStart) {
      // 微信小游戏环境
      this.eventListeners.touchstart = this.onTouchStart.bind(this);
      wx.onTouchStart(this.eventListeners.touchstart);

      this.eventListeners.touchmove = this.onTouchMove.bind(this);
      wx.onTouchMove(this.eventListeners.touchmove);

      this.eventListeners.touchend = this.onTouchEnd.bind(this);
      wx.onTouchEnd(this.eventListeners.touchend);

      this.eventListeners.touchcancel = this.onTouchCancel.bind(this);
      wx.onTouchCancel(this.eventListeners.touchcancel);
    } else {
      // 浏览器环境
      const canvas = window.canvas || document.getElementById('gameCanvas');
      if (canvas) {
        this.eventListeners.touchstart = this.onTouchStart.bind(this);
        canvas.addEventListener('touchstart', this.eventListeners.touchstart);
        canvas.addEventListener('mousedown', this.eventListeners.touchstart);

        this.eventListeners.touchmove = this.onTouchMove.bind(this);
        canvas.addEventListener('touchmove', this.eventListeners.touchmove);
        canvas.addEventListener('mousemove', this.eventListeners.touchmove);

        this.eventListeners.touchend = this.onTouchEnd.bind(this);
        canvas.addEventListener('touchend', this.eventListeners.touchend);
        canvas.addEventListener('mouseup', this.eventListeners.touchend);

        this.eventListeners.touchcancel = this.onTouchCancel.bind(this);
        canvas.addEventListener('touchcancel', this.eventListeners.touchcancel);
      }
    }
  }
  
  /**
   * 注销触摸事件
   */
  unregisterTouchEvents() {
    // 浏览器环境兼容
    if (typeof wx !== 'undefined' && wx.offTouchStart) {
      // 微信小游戏环境
      if (this.eventListeners.touchstart) {
        wx.offTouchStart(this.eventListeners.touchstart);
      }

      if (this.eventListeners.touchmove) {
        wx.offTouchMove(this.eventListeners.touchmove);
      }

      if (this.eventListeners.touchend) {
        wx.offTouchEnd(this.eventListeners.touchend);
      }

      if (this.eventListeners.touchcancel) {
        wx.offTouchCancel(this.eventListeners.touchcancel);
      }
    } else {
      // 浏览器环境
      const canvas = window.canvas || document.getElementById('gameCanvas');
      if (canvas) {
        if (this.eventListeners.touchstart) {
          canvas.removeEventListener('touchstart', this.eventListeners.touchstart);
          canvas.removeEventListener('mousedown', this.eventListeners.touchstart);
        }

        if (this.eventListeners.touchmove) {
          canvas.removeEventListener('touchmove', this.eventListeners.touchmove);
          canvas.removeEventListener('mousemove', this.eventListeners.touchmove);
        }

        if (this.eventListeners.touchend) {
          canvas.removeEventListener('touchend', this.eventListeners.touchend);
          canvas.removeEventListener('mouseup', this.eventListeners.touchend);
        }

        if (this.eventListeners.touchcancel) {
          canvas.removeEventListener('touchcancel', this.eventListeners.touchcancel);
        }
      }
    }
  }
  
  /**
   * 触摸开始事件处理
   * @param {Object} e - 触摸事件对象
   */
  onTouchStart(e) {
    if (!this.active) return;

    // 处理子场景的触摸事件
    for (let i = this.children.length - 1; i >= 0; i--) {
      const child = this.children[i];
      if (child.active && child.onTouchStart) {
        const handled = child.onTouchStart(e);
        if (handled) return;
      }
    }

    // 获取触摸点坐标（兼容鼠标和触摸事件）
    let x, y;
    if (e.touches && e.touches.length > 0) {
      // 触摸事件
      const touch = e.touches[0];
      const canvas = window.canvas || document.getElementById('gameCanvas');
      const rect = canvas.getBoundingClientRect();
      x = touch.clientX - rect.left;
      y = touch.clientY - rect.top;
    } else {
      // 鼠标事件
      const canvas = window.canvas || document.getElementById('gameCanvas');
      const rect = canvas.getBoundingClientRect();
      x = e.clientX - rect.left;
      y = e.clientY - rect.top;
    }

    // 从后往前检查元素，以便上层元素先响应
    for (let i = this.elements.length - 1; i >= 0; i--) {
      const element = this.elements[i];
      if (element.checkClick && element.checkClick(x, y)) {
        if (element.onClick) {
          element.onClick();
          return true;
        }
      }
    }

    return false;
  }
  
  /**
   * 触摸移动事件处理
   * @param {Object} e - 触摸事件对象
   */
  onTouchMove(e) {
    if (!this.active) return;
    
    // 处理子场景的触摸事件
    for (let i = this.children.length - 1; i >= 0; i--) {
      const child = this.children[i];
      if (child.active && child.onTouchMove) {
        const handled = child.onTouchMove(e);
        if (handled) return;
      }
    }
    
    return false;
  }
  
  /**
   * 触摸结束事件处理
   * @param {Object} e - 触摸事件对象
   */
  onTouchEnd(e) {
    if (!this.active) return;
    
    // 处理子场景的触摸事件
    for (let i = this.children.length - 1; i >= 0; i--) {
      const child = this.children[i];
      if (child.active && child.onTouchEnd) {
        const handled = child.onTouchEnd(e);
        if (handled) return;
      }
    }
    
    return false;
  }
  
  /**
   * 触摸取消事件处理
   * @param {Object} e - 触摸事件对象
   */
  onTouchCancel(e) {
    if (!this.active) return;
    
    // 处理子场景的触摸事件
    for (let i = this.children.length - 1; i >= 0; i--) {
      const child = this.children[i];
      if (child.active && child.onTouchCancel) {
        const handled = child.onTouchCancel(e);
        if (handled) return;
      }
    }
    
    return false;
  }
} 