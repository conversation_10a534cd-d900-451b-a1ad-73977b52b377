// TinyEmitter - 简单的事件发射器
class TinyEmitter {
  constructor() {
    this.e = {};
  }

  on(event, fn, ctx) {
    const events = this.e || (this.e = {});
    (events[event] || (events[event] = [])).push({ fn: fn, ctx: ctx });
    return this;
  }

  once(event, fn, ctx) {
    const self = this;
    function listener() {
      self.off(event, listener);
      fn.apply(ctx, arguments);
    }
    listener._ = fn;
    return this.on(event, listener, ctx);
  }

  emit(event) {
    const args = [].slice.call(arguments, 1);
    const events = ((this.e || (this.e = {}))[event] || []).slice();
    let i = 0;
    const len = events.length;
    for (i; i < len; i++) {
      events[i].fn.apply(events[i].ctx, args);
    }
    return this;
  }

  off(event, fn) {
    const events = this.e || (this.e = {});
    const listeners = events[event];
    const liveEvents = [];
    if (listeners && fn) {
      for (let i = 0, len = listeners.length; i < len; i++) {
        if (listeners[i].fn !== fn && listeners[i].fn._ !== fn) {
          liveEvents.push(listeners[i]);
        }
      }
    }
    liveEvents.length ? events[event] = liveEvents : delete events[event];
    return this;
  }
}

// ES6 模块导出
export default TinyEmitter;