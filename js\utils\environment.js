/**
 * 环境检测和适配工具
 * 负责检测运行环境并提供统一的API接口
 */

/**
 * 环境类型枚举
 */
export const ENVIRONMENT_TYPES = {
  WECHAT_GAME: 'wechat_game',
  BROWSER: 'browser',
  NODE: 'node',
  UNKNOWN: 'unknown'
};

/**
 * 环境检测器
 */
class EnvironmentDetector {
  constructor() {
    this._environmentType = null;
    this._capabilities = null;
    this.detectEnvironment();
  }

  /**
   * 检测当前运行环境
   */
  detectEnvironment() {
    // 检测微信小游戏环境
    if (typeof wx !== 'undefined' && wx.getSystemInfoSync) {
      this._environmentType = ENVIRONMENT_TYPES.WECHAT_GAME;
      return;
    }

    // 检测浏览器环境
    if (typeof window !== 'undefined' && typeof document !== 'undefined') {
      this._environmentType = ENVIRONMENT_TYPES.BROWSER;
      return;
    }

    // 检测Node.js环境
    if (typeof global !== 'undefined' && typeof process !== 'undefined') {
      this._environmentType = ENVIRONMENT_TYPES.NODE;
      return;
    }

    // 未知环境
    this._environmentType = ENVIRONMENT_TYPES.UNKNOWN;
  }

  /**
   * 获取环境类型
   */
  getEnvironmentType() {
    return this._environmentType;
  }

  /**
   * 检查是否为微信小游戏环境
   */
  isWeChatGame() {
    return this._environmentType === ENVIRONMENT_TYPES.WECHAT_GAME;
  }

  /**
   * 检查是否为浏览器环境
   */
  isBrowser() {
    return this._environmentType === ENVIRONMENT_TYPES.BROWSER;
  }

  /**
   * 检查是否为Node.js环境
   */
  isNode() {
    return this._environmentType === ENVIRONMENT_TYPES.NODE;
  }

  /**
   * 获取环境能力
   */
  getCapabilities() {
    if (this._capabilities) {
      return this._capabilities;
    }

    this._capabilities = {
      hasCanvas: this.hasCanvas(),
      hasDocument: this.hasDocument(),
      hasWindow: this.hasWindow(),
      hasAudio: this.hasAudio(),
      hasLocalStorage: this.hasLocalStorage(),
      hasTouch: this.hasTouch(),
      hasGamepad: this.hasGamepad()
    };

    return this._capabilities;
  }

  /**
   * 检查是否支持Canvas
   */
  hasCanvas() {
    if (this.isWeChatGame()) {
      return typeof wx.createCanvas === 'function';
    }
    
    if (this.isBrowser()) {
      return typeof HTMLCanvasElement !== 'undefined';
    }
    
    return false;
  }

  /**
   * 检查是否有document对象
   */
  hasDocument() {
    return typeof document !== 'undefined' && document.getElementById;
  }

  /**
   * 检查是否有window对象
   */
  hasWindow() {
    return typeof window !== 'undefined';
  }

  /**
   * 检查是否支持音频
   */
  hasAudio() {
    if (this.isWeChatGame()) {
      return typeof wx.createInnerAudioContext === 'function';
    }
    
    if (this.isBrowser()) {
      return typeof Audio !== 'undefined';
    }
    
    return false;
  }

  /**
   * 检查是否支持本地存储
   */
  hasLocalStorage() {
    if (this.isWeChatGame()) {
      return typeof wx.setStorageSync === 'function';
    }
    
    if (this.isBrowser()) {
      try {
        return typeof localStorage !== 'undefined' && localStorage.setItem;
      } catch (e) {
        return false;
      }
    }
    
    return false;
  }

  /**
   * 检查是否支持触摸
   */
  hasTouch() {
    if (this.isWeChatGame()) {
      return typeof wx.onTouchStart === 'function';
    }
    
    if (this.isBrowser()) {
      return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    }
    
    return false;
  }

  /**
   * 检查是否支持手柄
   */
  hasGamepad() {
    if (this.isBrowser()) {
      return typeof navigator.getGamepads === 'function';
    }
    
    return false;
  }
}

/**
 * 环境适配器
 */
class EnvironmentAdapter {
  constructor() {
    this.detector = new EnvironmentDetector();
  }

  /**
   * 获取Canvas元素
   */
  getCanvas() {
    // 优先使用已存在的canvas引用
    if (this.detector.hasWindow() && window.canvas) {
      return window.canvas;
    }

    // 微信小游戏环境
    if (this.detector.isWeChatGame()) {
      return wx.createCanvas();
    }

    // 浏览器环境
    if (this.detector.hasDocument()) {
      return document.getElementById('gameCanvas');
    }

    // 创建虚拟Canvas（用于测试或不支持的环境）
    return this.createMockCanvas();
  }

  /**
   * 创建虚拟Canvas
   */
  createMockCanvas() {
    console.warn('创建虚拟Canvas，某些功能可能不可用');
    return {
      width: 800,
      height: 600,
      getContext: () => this.createMockContext(),
      getBoundingClientRect: () => ({ left: 0, top: 0, width: 800, height: 600 }),
      addEventListener: () => {},
      removeEventListener: () => {}
    };
  }

  /**
   * 创建虚拟Canvas上下文
   */
  createMockContext() {
    const mockMethods = [
      'clearRect', 'fillRect', 'strokeRect', 'fillText', 'strokeText',
      'drawImage', 'save', 'restore', 'translate', 'rotate', 'scale',
      'beginPath', 'closePath', 'moveTo', 'lineTo', 'arc', 'fill', 'stroke'
    ];

    const context = {};
    mockMethods.forEach(method => {
      context[method] = () => {};
    });

    // 添加属性
    context.fillStyle = '#000000';
    context.strokeStyle = '#000000';
    context.lineWidth = 1;
    context.font = '12px Arial';
    context.textAlign = 'start';
    context.textBaseline = 'alphabetic';

    return context;
  }

  /**
   * 获取窗口信息
   */
  getWindowInfo() {
    if (this.detector.isWeChatGame()) {
      return wx.getSystemInfoSync();
    }

    if (this.detector.hasWindow()) {
      return {
        screenWidth: window.innerWidth,
        screenHeight: window.innerHeight,
        windowWidth: window.innerWidth,
        windowHeight: window.innerHeight,
        pixelRatio: window.devicePixelRatio || 1
      };
    }

    // 默认值
    return {
      screenWidth: 800,
      screenHeight: 600,
      windowWidth: 800,
      windowHeight: 600,
      pixelRatio: 1
    };
  }

  /**
   * 显示错误信息
   */
  showError(message) {
    console.error('环境适配器错误:', message);

    if (this.detector.isWeChatGame()) {
      wx.showToast({
        title: message,
        icon: 'none'
      });
    } else if (this.detector.isBrowser()) {
      alert(message);
    }
  }
}

// 创建全局实例
export const environmentDetector = new EnvironmentDetector();
export const environmentAdapter = new EnvironmentAdapter();

// 导出便捷方法
export const isWeChatGame = () => environmentDetector.isWeChatGame();
export const isBrowser = () => environmentDetector.isBrowser();
export const getCanvas = () => environmentAdapter.getCanvas();
export const getWindowInfo = () => environmentAdapter.getWindowInfo();
export const showError = (message) => environmentAdapter.showError(message);
